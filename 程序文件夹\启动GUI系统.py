#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
污染源异常检测GUI系统启动脚本
"""

import sys
import os
import traceback

def check_dependencies():
    """检查依赖项"""
    print("检查系统依赖项...")
    
    required_modules = [
        ('tkinter', 'GUI界面'),
        ('pandas', '数据处理'),
        ('numpy', '数值计算'),
        ('matplotlib', '图表生成'),
        ('openpyxl', 'Excel文件读写')
    ]
    
    missing_modules = []
    
    for module_name, description in required_modules:
        try:
            __import__(module_name)
            print(f"✓ {module_name} ({description})")
        except ImportError:
            print(f"✗ {module_name} ({description}) - 缺失")
            missing_modules.append(module_name)
    
    if missing_modules:
        print(f"\n⚠️ 缺少以下依赖模块: {', '.join(missing_modules)}")
        print("请使用以下命令安装:")
        print(f"pip install {' '.join(missing_modules)}")
        return False
    
    print("✓ 所有依赖项检查通过")
    return True

def check_data_files():
    """检查数据文件"""
    print("\n检查数据文件...")
    
    data_folder = "../数据读取"
    if not os.path.exists(data_folder):
        print(f"⚠️ 数据文件夹不存在: {data_folder}")
        print("请确保数据文件夹存在并包含Excel数据文件")
        return False
    
    excel_files = [f for f in os.listdir(data_folder) if f.endswith(('.xlsx', '.xls'))]
    
    if not excel_files:
        print(f"⚠️ 在 {data_folder} 中没有找到Excel数据文件")
        print("请将Excel数据文件放入数据读取文件夹")
        return False
    
    print(f"✓ 找到 {len(excel_files)} 个Excel数据文件")
    for file in excel_files[:3]:  # 显示前3个文件
        print(f"  - {file}")
    
    if len(excel_files) > 3:
        print(f"  ... 还有 {len(excel_files) - 3} 个文件")
    
    return True

def create_output_directory():
    """创建输出目录"""
    print("\n创建输出目录...")
    
    output_dir = "../检测报告"
    try:
        os.makedirs(output_dir, exist_ok=True)
        print(f"✓ 输出目录准备就绪: {output_dir}")
        return True
    except Exception as e:
        print(f"✗ 创建输出目录失败: {str(e)}")
        return False

def launch_gui():
    """启动GUI系统"""
    try:
        # 重定向标准输入输出，避免PyInstaller打包后的stdin/stdout问题
        import sys
        import io

        # 检查是否在PyInstaller环境中运行
        if getattr(sys, 'frozen', False):
            # 在打包环境中，重定向标准输入输出
            sys.stdin = io.StringIO()
            sys.stdout = io.StringIO()
            sys.stderr = io.StringIO()

        from 污染源异常检测GUI系统 import main

        # 启动GUI
        main()

    except KeyboardInterrupt:
        pass  # 静默处理用户中断
    except Exception as e:
        # 在打包环境中，使用messagebox显示错误而不是print
        try:
            import tkinter as tk
            from tkinter import messagebox

            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口

            error_msg = f"程序启动失败:\n{str(e)}\n\n请检查:\n1. 系统环境是否满足要求\n2. 是否有足够的磁盘空间\n3. 数据文件夹是否存在"
            messagebox.showerror("启动错误", error_msg)

            root.destroy()
        except:
            pass  # 如果连messagebox都无法显示，则静默失败

        return False

    return True

def main():
    """主函数"""
    print("污染源异常检测GUI系统 V6.0")
    print("=" * 40)
    print("正在进行系统初始化检查...\n")
    
    # 系统检查
    checks = [
        ("依赖项检查", check_dependencies),
        ("数据文件检查", check_data_files),
        ("输出目录创建", create_output_directory)
    ]
    
    for check_name, check_func in checks:
        if not check_func():
            print(f"\n❌ {check_name}失败，程序无法启动")
            print("\n请解决上述问题后重新运行程序")
            input("\n按回车键退出...")
            return False
    
    print("\n✅ 系统检查完成，所有项目正常")
    print("\n准备启动GUI界面...")
    
    # 启动GUI
    success = launch_gui()
    
    if not success:
        input("\n按回车键退出...")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n程序异常退出: {str(e)}")
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)
