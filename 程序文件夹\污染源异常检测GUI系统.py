#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
污染源流量异常检测系统V6.0 - GUI版本
基于V6.0时间段简化系统的图形用户界面应用程序
保持原有算法逻辑、检测结果和系统架构完全不变
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import json
from datetime import datetime
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.dates as mdates
from matplotlib.dates import DateFormatter

# 导入V6.0系统核心模块
from 简化城市污染源系统 import CityAnomalyDetectionSystem
from V6_0核心算法模块 import (
    perform_time_segment_analysis_with_config,
    generate_time_segment_visualizations_with_config
)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class PollutionDetectionGUI:
    """污染源异常检测GUI主类"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("污染源流量异常检测系统 V6.0")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # 系统状态变量
        self.data_file_path = None
        self.system = None
        self.is_running = False
        self.results = None
        
        # 默认参数配置
        self.config = {
            # 间断点分割参数
            'window_size': 24,  # 最小滑动窗口值
            'cv_threshold': 0.8,  # 间断点固定阈值
            'fixed_threshold': 0.5,  # 差分阈值参数
            'min_distance': 12,  # 最小距离过滤区间参数
            
            # 模式判定参数
            'shutdown_threshold': 0.7,  # 停运零值/极低值比例
            'low_value_limit': 0.2,  # 极低值上限定义
            'single_state_cv_max': 0.3,  # 单状态变异系数上限
            'normal_fluctuation_cv_min': 0.3,  # 正常波动变异系数下限
            
            # 异常检测参数
            'protection_std_small': 0.1,  # 数值保护区间计算参数 - 小标准差阈值
            'protection_std_medium': 0.5,  # 数值保护区间计算参数 - 中等标准差阈值
            'protection_factor_small': 1.5,  # 小标准差调整系数
            'protection_factor_medium': 1.0,  # 中等标准差调整系数
            'protection_factor_large': 0.5,  # 大标准差调整系数
            
            # 检测方法选择
            'use_p5_p95': True,  # P5/P95方法
            'use_iqr': True,  # IQR方法
            'use_mad': True,  # MAD方法
            
            # 方法权重
            'p5_p95_weight': 3,  # P5/P95可信度评分权重
            'iqr_weight': 3,  # IQR可信度评分权重
            'mad_weight': 3,  # MAD可信度评分权重
            'negative_weight': 5,  # 负值异常权重
            
            # MAD参数
            'mad_multiplier': 2.5,  # MAD倍数
            
            # IQR参数
            'iqr_multiplier': 1.5,  # IQR倍数
        }
        
        self.setup_ui()
        self.load_config()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个模块选项卡
        self.setup_data_input_tab()
        self.setup_parameter_config_tab()
        self.setup_execution_tab()
        self.setup_documentation_tab()
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))
    
    def setup_data_input_tab(self):
        """设置数据输入模块"""
        self.data_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.data_frame, text="数据输入")
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(self.data_frame, text="数据文件选择", padding=10)
        file_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 文件路径显示
        ttk.Label(file_frame, text="选择Excel数据文件:").pack(anchor=tk.W)
        
        path_frame = ttk.Frame(file_frame)
        path_frame.pack(fill=tk.X, pady=5)
        
        self.file_path_var = tk.StringVar()
        self.file_path_entry = ttk.Entry(path_frame, textvariable=self.file_path_var, state='readonly')
        self.file_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Button(path_frame, text="浏览", command=self.browse_file).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 数据预览区域
        preview_frame = ttk.LabelFrame(self.data_frame, text="数据预览", padding=10)
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建表格
        columns = ('企业名称', '监测点名称', '时间', '流量值', '数据月份')
        self.data_tree = ttk.Treeview(preview_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=120)
        
        # 滚动条
        scrollbar_y = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.data_tree.yview)
        scrollbar_x = ttk.Scrollbar(preview_frame, orient=tk.HORIZONTAL, command=self.data_tree.xview)
        self.data_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        self.data_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 数据统计信息
        self.data_info_var = tk.StringVar()
        self.data_info_var.set("未加载数据")
        ttk.Label(preview_frame, textvariable=self.data_info_var).pack(pady=5)
    
    def setup_parameter_config_tab(self):
        """设置参数配置模块"""
        self.param_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.param_frame, text="参数配置")
        
        # 创建滚动框架
        canvas = tk.Canvas(self.param_frame)
        scrollbar = ttk.Scrollbar(self.param_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 间断点分割参数
        breakpoint_frame = ttk.LabelFrame(scrollable_frame, text="间断点分割参数", padding=10)
        breakpoint_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.create_parameter_input(breakpoint_frame, "最小滑动窗口值:", "window_size", 1, 100)
        self.create_parameter_input(breakpoint_frame, "间断点固定阈值:", "cv_threshold", 0.1, 2.0, 0.1)
        self.create_parameter_input(breakpoint_frame, "差分阈值参数:", "fixed_threshold", 0.1, 2.0, 0.1)
        self.create_parameter_input(breakpoint_frame, "最小距离过滤区间:", "min_distance", 1, 48)
        
        # 模式判定参数
        mode_frame = ttk.LabelFrame(scrollable_frame, text="模式判定参数", padding=10)
        mode_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.create_parameter_input(mode_frame, "停运零值/极低值比例:", "shutdown_threshold", 0.1, 1.0, 0.05)
        self.create_parameter_input(mode_frame, "极低值上限定义:", "low_value_limit", 0.01, 1.0, 0.01)
        self.create_parameter_input(mode_frame, "单状态变异系数上限:", "single_state_cv_max", 0.1, 1.0, 0.05)
        self.create_parameter_input(mode_frame, "正常波动变异系数下限:", "normal_fluctuation_cv_min", 0.1, 1.0, 0.05)

        # 异常检测参数
        detection_frame = ttk.LabelFrame(scrollable_frame, text="异常检测参数", padding=10)
        detection_frame.pack(fill=tk.X, padx=10, pady=5)

        self.create_parameter_input(detection_frame, "小标准差阈值:", "protection_std_small", 0.01, 1.0, 0.01)
        self.create_parameter_input(detection_frame, "中等标准差阈值:", "protection_std_medium", 0.1, 2.0, 0.1)
        self.create_parameter_input(detection_frame, "小标准差调整系数:", "protection_factor_small", 0.5, 3.0, 0.1)
        self.create_parameter_input(detection_frame, "中等标准差调整系数:", "protection_factor_medium", 0.5, 3.0, 0.1)
        self.create_parameter_input(detection_frame, "大标准差调整系数:", "protection_factor_large", 0.1, 2.0, 0.1)

        # 检测方法选择
        method_frame = ttk.LabelFrame(scrollable_frame, text="检测方法选择", padding=10)
        method_frame.pack(fill=tk.X, padx=10, pady=5)

        self.create_checkbox_input(method_frame, "使用P5/P95方法:", "use_p5_p95")
        self.create_checkbox_input(method_frame, "使用IQR方法:", "use_iqr")
        self.create_checkbox_input(method_frame, "使用MAD方法:", "use_mad")

        # 方法权重参数
        weight_frame = ttk.LabelFrame(scrollable_frame, text="方法权重参数", padding=10)
        weight_frame.pack(fill=tk.X, padx=10, pady=5)

        self.create_parameter_input(weight_frame, "P5/P95权重:", "p5_p95_weight", 1, 10, 1)
        self.create_parameter_input(weight_frame, "IQR权重:", "iqr_weight", 1, 10, 1)
        self.create_parameter_input(weight_frame, "MAD权重:", "mad_weight", 1, 10, 1)
        self.create_parameter_input(weight_frame, "负值异常权重:", "negative_weight", 1, 10, 1)

        # 算法参数
        algorithm_frame = ttk.LabelFrame(scrollable_frame, text="算法参数", padding=10)
        algorithm_frame.pack(fill=tk.X, padx=10, pady=5)

        self.create_parameter_input(algorithm_frame, "MAD倍数:", "mad_multiplier", 1.0, 5.0, 0.1)
        self.create_parameter_input(algorithm_frame, "IQR倍数:", "iqr_multiplier", 1.0, 3.0, 0.1)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_parameter_input(self, parent, label, key, min_val, max_val, increment=1):
        """创建参数输入控件"""
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(frame, text=label, width=25).pack(side=tk.LEFT)
        
        var = tk.DoubleVar() if isinstance(increment, float) else tk.IntVar()
        var.set(self.config[key])
        
        spinbox = ttk.Spinbox(frame, from_=min_val, to=max_val, increment=increment, 
                             textvariable=var, width=10)
        spinbox.pack(side=tk.LEFT, padx=5)
        
        # 保存变量引用
        setattr(self, f"{key}_var", var)
        
        # 绑定变化事件
        var.trace('w', lambda *args, k=key, v=var: self.update_config(k, v.get()))

    def create_checkbox_input(self, parent, label, key):
        """创建复选框输入控件"""
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.X, pady=2)

        var = tk.BooleanVar()
        var.set(self.config[key])

        checkbox = ttk.Checkbutton(frame, text=label, variable=var)
        checkbox.pack(side=tk.LEFT)

        # 保存变量引用
        setattr(self, f"{key}_var", var)

        # 绑定变化事件
        var.trace('w', lambda *args, k=key, v=var: self.update_config(k, v.get()))

    def update_config(self, key, value):
        """更新配置参数"""
        self.config[key] = value
    
    def setup_execution_tab(self):
        """设置程序执行模块"""
        self.exec_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.exec_frame, text="程序执行")
        
        # 控制按钮区域
        control_frame = ttk.Frame(self.exec_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.run_button = ttk.Button(control_frame, text="开始分析", command=self.run_analysis)
        self.run_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="停止分析", command=self.stop_analysis, state='disabled')
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(control_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="加载配置", command=self.load_config).pack(side=tk.LEFT, padx=5)
        
        # 进度显示区域
        progress_frame = ttk.LabelFrame(self.exec_frame, text="执行进度", padding=10)
        progress_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.progress_var = tk.StringVar()
        self.progress_var.set("等待开始...")
        ttk.Label(progress_frame, textvariable=self.progress_var).pack(anchor=tk.W)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=5)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(self.exec_frame, text="执行日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, state='disabled')
        self.log_text.pack(fill=tk.BOTH, expand=True)
    
    def setup_documentation_tab(self):
        """设置系统说明模块"""
        self.doc_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.doc_frame, text="系统说明")
        
        # 工具栏
        toolbar = ttk.Frame(self.doc_frame)
        toolbar.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(toolbar, text="保存说明", command=self.save_documentation).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar, text="加载说明", command=self.load_documentation).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar, text="重置为默认", command=self.reset_documentation).pack(side=tk.LEFT, padx=5)
        
        # 文档编辑区域
        self.doc_text = scrolledtext.ScrolledText(self.doc_frame, wrap=tk.WORD)
        self.doc_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 加载默认文档内容
        self.load_default_documentation()

    def browse_file(self):
        """浏览并选择数据文件"""
        file_path = filedialog.askopenfilename(
            title="选择Excel数据文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )

        if file_path:
            self.file_path_var.set(file_path)
            self.data_file_path = file_path
            self.load_data_preview()

    def load_data_preview(self):
        """加载数据预览"""
        if not self.data_file_path:
            return

        try:
            # 清空现有数据
            for item in self.data_tree.get_children():
                self.data_tree.delete(item)

            # 读取Excel文件
            df = pd.read_excel(self.data_file_path)

            # 显示前100行数据
            display_rows = min(100, len(df))

            for i in range(display_rows):
                row = df.iloc[i]
                # 提取关键列
                company_name = row.get('企业名称', 'N/A')
                site_name = row.get('监测点名称', 'N/A')
                data_time = row.get('data_time', 'N/A')
                flow_value = row.get('流量', 'N/A')
                month = row.get('数据月份', 'N/A')

                self.data_tree.insert('', 'end', values=(
                    str(company_name)[:20],
                    str(site_name)[:20],
                    str(data_time)[:20],
                    str(flow_value),
                    str(month)
                ))

            # 更新统计信息
            info_text = f"总记录数: {len(df)}, 显示前 {display_rows} 条记录"
            if len(df) > display_rows:
                info_text += f" (共 {len(df)} 条)"

            self.data_info_var.set(info_text)
            self.log_message(f"成功加载数据文件: {os.path.basename(self.data_file_path)}")

        except Exception as e:
            messagebox.showerror("错误", f"加载数据文件失败:\n{str(e)}")
            self.log_message(f"数据加载失败: {str(e)}")

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.config(state='normal')
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.log_text.config(state='disabled')

    def run_analysis(self):
        """运行分析"""
        if not self.data_file_path:
            messagebox.showwarning("警告", "请先选择数据文件")
            return

        if self.is_running:
            messagebox.showwarning("警告", "分析正在进行中")
            return

        # 启动分析线程
        self.is_running = True
        self.run_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.progress_bar.start()
        self.progress_var.set("正在初始化...")

        analysis_thread = threading.Thread(target=self.perform_analysis)
        analysis_thread.daemon = True
        analysis_thread.start()

    def perform_analysis(self):
        """执行分析（在后台线程中运行）"""
        try:
            self.log_message("开始V6.0污染源异常检测分析...")

            # 更新进度
            self.root.after(0, lambda: self.progress_var.set("正在加载数据..."))

            # 创建系统实例
            city_name = "数据分析"  # 可以从文件名提取
            self.system = CityAnomalyDetectionSystem(city_name)

            # 直接加载选定的文件
            df = pd.read_excel(self.data_file_path)

            # 检查必要的列
            required_columns = ['企业名称', '监测点名称', 'data_time', '流量']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                raise ValueError(f"数据文件缺少必要的列: {missing_columns}")

            # 添加月份信息（如果没有的话）
            if '数据月份' not in df.columns:
                # 尝试从data_time提取月份
                df['数据月份'] = pd.to_datetime(df['data_time']).dt.month

            self.log_message(f"成功加载 {len(df)} 条记录")

            # 预处理数据
            self.root.after(0, lambda: self.progress_var.set("正在预处理数据..."))
            self.system.processed_data = self.system._preprocess_data(df)
            self.system._organize_monthly_data()

            # 执行V6.0分析
            self.root.after(0, lambda: self.progress_var.set("正在执行时间段分析..."))

            # 使用配置参数执行分析
            def progress_update(message):
                self.root.after(0, lambda: self.progress_var.set(message))

            segment_results = perform_time_segment_analysis_with_config(
                self.system, self.config, progress_update
            )

            self.root.after(0, lambda: self.progress_var.set("正在生成可视化图表..."))

            # 创建输出目录
            timestamp = datetime.now().strftime("%m-%d-%H-%M-%S")
            output_dir = os.path.join("..", "检测报告", timestamp)
            os.makedirs(output_dir, exist_ok=True)

            chart_results = generate_time_segment_visualizations_with_config(
                self.system, segment_results, output_dir, self.config, progress_update
            )

            self.results = {
                'segment_results': segment_results,
                'charts': chart_results,
                'output_dir': output_dir
            }

            # 分析完成
            self.root.after(0, self.analysis_completed)

        except Exception as e:
            error_msg = f"分析过程出错: {str(e)}"
            self.root.after(0, lambda: self.analysis_failed(error_msg))

    def analysis_completed(self):
        """分析完成处理"""
        self.is_running = False
        self.run_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress_bar.stop()
        self.progress_var.set("分析完成")

        if self.results:
            total_charts = self.results['charts'].get('total_charts', 0)
            output_dir = self.results['output_dir']

            self.log_message(f"分析完成！生成图表 {total_charts} 个")
            self.log_message(f"输出目录: {output_dir}")

            # 显示完成对话框
            result = messagebox.askquestion(
                "分析完成",
                f"分析已完成！\n生成图表: {total_charts} 个\n输出目录: {output_dir}\n\n是否打开输出目录？"
            )

            if result == 'yes':
                try:
                    os.startfile(output_dir)
                except:
                    self.log_message("无法打开输出目录")

    def analysis_failed(self, error_msg):
        """分析失败处理"""
        self.is_running = False
        self.run_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress_bar.stop()
        self.progress_var.set("分析失败")

        self.log_message(error_msg)
        messagebox.showerror("分析失败", error_msg)

    def stop_analysis(self):
        """停止分析"""
        self.is_running = False
        self.run_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress_bar.stop()
        self.progress_var.set("已停止")
        self.log_message("用户停止了分析")

    def save_config(self):
        """保存配置"""
        try:
            config_file = filedialog.asksaveasfilename(
                title="保存配置文件",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if config_file:
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, indent=2, ensure_ascii=False)

                self.log_message(f"配置已保存到: {config_file}")
                messagebox.showinfo("成功", "配置保存成功")

        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败:\n{str(e)}")

    def load_config(self):
        """加载配置"""
        try:
            config_file = filedialog.askopenfilename(
                title="加载配置文件",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if config_file:
                with open(config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)

                # 更新配置
                self.config.update(loaded_config)

                # 更新界面控件
                self.update_parameter_widgets()

                self.log_message(f"配置已从文件加载: {config_file}")
                messagebox.showinfo("成功", "配置加载成功")

        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败:\n{str(e)}")

    def update_parameter_widgets(self):
        """更新参数控件的值"""
        for key, value in self.config.items():
            var_name = f"{key}_var"
            if hasattr(self, var_name):
                var = getattr(self, var_name)
                var.set(value)

    def save_documentation(self):
        """保存系统说明文档"""
        try:
            doc_file = filedialog.asksaveasfilename(
                title="保存系统说明文档",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )

            if doc_file:
                content = self.doc_text.get(1.0, tk.END)
                with open(doc_file, 'w', encoding='utf-8') as f:
                    f.write(content)

                self.log_message(f"系统说明已保存到: {doc_file}")
                messagebox.showinfo("成功", "系统说明保存成功")

        except Exception as e:
            messagebox.showerror("错误", f"保存系统说明失败:\n{str(e)}")

    def load_documentation(self):
        """加载系统说明文档"""
        try:
            doc_file = filedialog.askopenfilename(
                title="加载系统说明文档",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )

            if doc_file:
                with open(doc_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                self.doc_text.delete(1.0, tk.END)
                self.doc_text.insert(1.0, content)

                self.log_message(f"系统说明已从文件加载: {doc_file}")
                messagebox.showinfo("成功", "系统说明加载成功")

        except Exception as e:
            messagebox.showerror("错误", f"加载系统说明失败:\n{str(e)}")

    def reset_documentation(self):
        """重置为默认系统说明"""
        result = messagebox.askquestion("确认", "确定要重置为默认系统说明吗？当前内容将丢失。")
        if result == 'yes':
            self.load_default_documentation()
            self.log_message("系统说明已重置为默认内容")

    def load_default_documentation(self):
        """加载默认系统说明文档"""
        default_doc = """# 污染源流量异常检测系统 V6.0 - 系统说明

## 系统概述
本系统基于V6.0时间段简化系统，采用先进的统计学方法和机器学习技术，对污染源监测数据进行智能化异常检测分析。

## 核心算法思路

### 1. 时间段划分方法
#### 分割点检测算法
- 使用滑动窗口（默认24小时）检测变异系数变化
- 三重条件检测：变异系数差异 > 0.8，均值差异 > 0.5，中位数差异 > 0.5
- 最小间隔：12小时（避免过度分割）
- 最大时间段数：8个（7个分割点）

#### 时间段独立性原则
- 每个时间段独立进行运行状态识别和异常检测
- 避免不同运行模式间的相互干扰
- 提高检测精度和可解释性

### 2. 简化运行状态识别（3种状态）
#### 停运状态
- 判定条件：零值或极低值（≤0.2）比例 ≥ 70%
- 数据特征：长期稳定在零值或极低值范围
- 置信度计算：0.5 + 零值比例（最高0.95）

#### 单状态运行
- 判定条件：零值比例 < 70% 且 变异系数 < 0.3
- 数据特征：数值相对稳定，变化幅度较小
- 置信度计算：0.5 + (1 - 变异系数)（最高0.95）

#### 正常波动
- 判定条件：零值比例 < 70% 且 变异系数 ≥ 0.3
- 数据特征：存在明显的规律性或随机性变化
- 置信度计算：0.5 + min(变异系数, 1.0)（最高0.95）

### 3. 简化异常检测方法
#### 停用的方法
- LOF（局部异常因子）：计算复杂，参数敏感
- DBSCAN聚类：对数据分布要求高
- 机器学习方法：需要大量训练数据

#### 启用的统计方法（等权重）
#### P5/P95百分位数法
- 计算：第5百分位数和第95百分位数
- 异常判定：值 < P5 或 值 > P95
- 可信度评分：3分

#### IQR四分位距法
- 计算：Q1-1.5×IQR 和 Q3+1.5×IQR
- 异常判定：值在IQR范围外
- 可信度评分：3分

#### MAD中位数绝对偏差法
- 计算：中位数 ± 2.5×MAD
- 异常判定：值在MAD范围外
- 可信度评分：3分

### 业务规则层
- 负值异常：所有负值直接判定为异常
- 可信度评分：5分（高于统计方法）

### 4. 区间保护机制
#### 保护区间计算
- 基准值：该时间段内数据的中位数
- 调整系数：根据标准差反向调整
  * 标准差 < 0.1：调整系数 = 1.5（扩大保护区间）
  * 标准差 0.1-0.5：调整系数 = 1.0（正常保护区间）
  * 标准差 > 0.5：调整系数 = 0.5（缩小保护区间）
- 保护区间 = 中位数 ± (标准差 × 调整系数)

#### 保护规则
- 统计方法检测到的异常，如果在保护区间内，则不判定为异常
- 负值异常不受保护区间限制
- 保护机制减少误报，提高检测精度

### 5. 可视化系统
#### 五色编码系统
- 蓝色圆点：正常数值
- 绿色圆点：零值
- 黑色叉号：负值异常
- 黄色圆点：统计异常
- 红色圆点：显著异常（高评分）

#### 时间序列图表
- X轴：按时间顺序排列（非数据点序列）
- Y轴：流量数值
- 绿色虚线：时间段分割点
- 状态标注：每个时间段的运行状态和置信度

### 6. 输出格式
#### Excel报告
- 时间段分析汇总表
- 异常检测详细结果
- 统计参数和评分
- 可视化图表文件引用

#### 图表文件
- PNG格式散点图
- 高分辨率，适合打印和展示
- 中文标题和图例
- 专业配色方案

## 技术参数
- 时间段检测窗口：24小时
- 变异系数阈值：0.8
- 固定阈值：0.5
- 最小时间段间隔：12小时
- 最大时间段数：8个
- 停运状态阈值：70%零值比例
- 单状态运行阈值：变异系数<0.3
- 保护区间调整系数：0.5-1.5

## 使用说明
1. 在"数据输入"选项卡中选择Excel数据文件
2. 在"参数配置"选项卡中调整检测参数
3. 在"程序执行"选项卡中点击"开始分析"
4. 查看实时执行日志和进度
5. 分析完成后查看输出报告和图表

## 注意事项
- 确保数据文件包含必要的列：企业名称、监测点名称、data_time、流量
- 建议数据量不少于100个数据点以获得可靠结果
- 参数调整需要根据具体数据特征进行优化
- 系统输出的图表和报告保存在"检测报告"文件夹中

生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
系统版本：V6.0 GUI版本
"""

        self.doc_text.delete(1.0, tk.END)
        self.doc_text.insert(1.0, default_doc)


def main():
    """主函数"""
    try:
        # 修复PyInstaller打包后的stdin/stdout问题
        import io

        # 检查是否在PyInstaller环境中运行
        if getattr(sys, 'frozen', False):
            # 在打包环境中，重定向标准输入输出
            if sys.stdin is None:
                sys.stdin = io.StringIO()
            if sys.stdout is None:
                sys.stdout = io.StringIO()
            if sys.stderr is None:
                sys.stderr = io.StringIO()

        # 创建主窗口
        root = tk.Tk()

        # 设置窗口图标（如果有的话）
        try:
            root.iconbitmap('icon.ico')
        except:
            pass  # 忽略图标加载失败

        # 创建GUI应用
        app = PollutionDetectionGUI(root)

        # 启动主循环
        root.mainloop()

    except Exception as e:
        try:
            messagebox.showerror("系统错误", f"程序启动失败:\n{str(e)}")
        except:
            pass  # 如果messagebox也失败，则静默退出
        sys.exit(1)


if __name__ == "__main__":
    main()
