#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
构建独立可执行文件的脚本
使用PyInstaller将GUI应用程序打包成.exe文件
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print("✓ PyInstaller已安装")
        return True
    except ImportError:
        print("✗ PyInstaller未安装")
        print("正在安装PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✓ PyInstaller安装成功")
            return True
        except subprocess.CalledProcessError:
            print("✗ PyInstaller安装失败")
            return False

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['启动GUI系统.py'],
    pathex=['.', '..', '../城市在线监测流量异常检测系统'],
    binaries=[],
    datas=[
        ('V6_0核心算法模块.py', '.'),
        ('污染源异常检测GUI系统.py', '.'),
        ('../城市在线监测流量异常检测系统/城市污染源异常检测系统.py', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'pandas',
        'numpy',
        'matplotlib',
        'matplotlib.pyplot',
        'matplotlib.backends.backend_tkagg',
        'matplotlib.dates',
        'sklearn',
        'sklearn.cluster',
        'sklearn.neighbors',
        'sklearn.preprocessing',
        'openpyxl',
        'xlrd',
        'datetime',
        'threading',
        'json',
        'os',
        'sys'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='污染源异常检测系统V6.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''

    try:
        with open('污染源异常检测系统.spec', 'w', encoding='utf-8') as f:
            f.write(spec_content)
        print("✓ PyInstaller规格文件创建成功")
        return True
    except Exception as e:
        print(f"✗ 创建规格文件失败: {str(e)}")
        return False

def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    print("这可能需要几分钟时间，请耐心等待...")
    
    try:
        # 使用PyInstaller构建
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "污染源异常检测系统.spec"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✓ 可执行文件构建成功")
            return True
        else:
            print("✗ 可执行文件构建失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 构建过程出错: {str(e)}")
        return False

def copy_to_target_directory():
    """将可执行文件复制到目标目录"""
    source_exe = "dist/污染源异常检测系统V6.0.exe"
    
    if not os.path.exists(source_exe):
        print(f"✗ 找不到生成的可执行文件: {source_exe}")
        return False
    
    # 目标目录就是当前程序文件夹
    target_exe = "污染源异常检测系统V6.0.exe"
    
    try:
        shutil.copy2(source_exe, target_exe)
        print(f"✓ 可执行文件已复制到: {os.path.abspath(target_exe)}")
        
        # 获取文件大小
        file_size = os.path.getsize(target_exe) / (1024 * 1024)  # MB
        print(f"✓ 文件大小: {file_size:.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"✗ 复制文件失败: {str(e)}")
        return False

def cleanup_build_files():
    """清理构建过程中的临时文件"""
    print("清理临时文件...")
    
    cleanup_dirs = ['build', 'dist', '__pycache__']
    cleanup_files = ['污染源异常检测系统.spec']
    
    for dir_name in cleanup_dirs:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✓ 删除临时目录: {dir_name}")
            except Exception as e:
                print(f"⚠️ 删除目录失败 {dir_name}: {str(e)}")
    
    for file_name in cleanup_files:
        if os.path.exists(file_name):
            try:
                os.remove(file_name)
                print(f"✓ 删除临时文件: {file_name}")
            except Exception as e:
                print(f"⚠️ 删除文件失败 {file_name}: {str(e)}")

def create_readme():
    """创建使用说明文件"""
    readme_content = f"""# 污染源异常检测系统 V6.0 - 使用说明

## 系统简介
本系统是基于V6.0时间段简化系统开发的图形用户界面版本，用于污染源监测数据的智能化异常检测分析。

## 系统要求
- Windows 7/8/10/11 (64位)
- 至少 4GB 内存
- 至少 500MB 可用磁盘空间

## 使用方法

### 1. 启动程序
双击 `污染源异常检测系统V6.0.exe` 启动程序

### 2. 数据输入
- 在"数据输入"选项卡中点击"浏览"按钮
- 选择包含污染源监测数据的Excel文件
- 确保Excel文件包含以下必要列：
  - 企业名称
  - 监测点名称  
  - data_time (时间)
  - 流量

### 3. 参数配置
在"参数配置"选项卡中可以调整以下参数：

#### 间断点分割参数
- 最小滑动窗口值: 默认24小时
- 间断点固定阈值: 默认0.8
- 差分阈值参数: 默认0.5
- 最小距离过滤区间: 默认12小时

#### 模式判定参数
- 停运零值/极低值比例: 默认0.7 (70%)
- 极低值上限定义: 默认0.2
- 单状态变异系数上限: 默认0.3
- 正常波动变异系数下限: 默认0.3

#### 异常检测参数
- 数值保护区间计算参数
- 检测方法选择 (P5/P95、IQR、MAD)
- 各方法可信度评分权重

### 4. 执行分析
- 在"程序执行"选项卡中点击"开始分析"
- 观察实时进度和执行日志
- 分析完成后会提示打开输出目录

### 5. 查看结果
分析结果包括：
- Excel格式的详细分析报告
- PNG格式的散点图可视化
- 时间段分析汇总表

## 输出文件说明

### 散点图颜色编码
- 蓝色圆点: 正常数值
- 绿色圆点: 零值
- 黑色叉号: 负值异常
- 黄色圆点: 统计异常
- 红色圆点: 显著异常

### 时间段标注
- 绿色虚线: 时间段分割点
- 文本标注: 各时间段的运行状态和置信度

## 注意事项
1. 确保数据文件格式正确，包含所有必要列
2. 建议数据量不少于100个数据点以获得可靠结果
3. 参数调整需要根据具体数据特征进行优化
4. 分析结果保存在程序同级目录的"检测报告"文件夹中

## 技术支持
如遇到问题，请检查：
1. 数据文件格式是否正确
2. 是否有足够的磁盘空间
3. 系统是否满足最低要求

构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
系统版本: V6.0 GUI版本
"""
    
    with open('使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✓ 使用说明文件创建成功")

def main():
    """主函数"""
    print("污染源异常检测系统 V6.0 - 可执行文件构建工具")
    print("=" * 60)
    
    # 检查和安装PyInstaller
    if not check_pyinstaller():
        print("❌ PyInstaller安装失败，无法继续构建")
        return False
    
    print("\n开始构建过程...")
    
    # 构建步骤
    steps = [
        ("创建规格文件", create_spec_file),
        ("构建可执行文件", build_executable),
        ("复制到目标目录", copy_to_target_directory),
        ("创建使用说明", create_readme),
        ("清理临时文件", cleanup_build_files)
    ]
    
    for step_name, step_func in steps:
        print(f"\n--- {step_name} ---")
        try:
            if not step_func():
                print(f"❌ {step_name}失败")
                return False
        except Exception as e:
            print(f"❌ {step_name}异常: {str(e)}")
            return False
    
    print("\n" + "=" * 60)
    print("🎉 可执行文件构建完成！")
    print("\n生成的文件:")
    print("- 污染源异常检测系统V6.0.exe (主程序)")
    print("- 使用说明.txt (使用说明)")
    print(f"\n文件位置: {os.path.abspath('.')}")
    print("\n现在可以双击 .exe 文件启动程序了！")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n构建失败，请检查错误信息")
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n用户中断构建过程")
    except Exception as e:
        print(f"\n构建过程异常: {str(e)}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
