#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V6.0污染源异常检测核心算法模块
从V6.0时间段简化系统中提取的核心算法，用于GUI集成
保持原有算法逻辑完全不变
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.dates import DateFormatter
from datetime import datetime
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def detect_breakpoints_with_config(flow_data, timestamps, config):
    """
    使用配置参数检测间断点
    
    Args:
        flow_data: 流量数据序列
        timestamps: 时间戳序列
        config: 配置参数字典
    
    Returns:
        list: 间断点时间戳列表
    """
    if len(flow_data) < config['window_size'] * 2:
        return []
    
    # 使用滑动窗口检测变异系数变化
    window_size = config['window_size']
    cv_threshold = config['cv_threshold']
    fixed_threshold = config['fixed_threshold']
    min_distance = config['min_distance']
    
    breakpoints = []
    
    # 计算滑动窗口变异系数
    cv_values = []
    cv_timestamps = []
    
    for i in range(window_size, len(flow_data) - window_size):
        window_data = flow_data[i-window_size//2:i+window_size//2]
        positive_data = window_data[window_data > 0]
        
        if len(positive_data) > 3:
            cv = np.std(positive_data) / np.mean(positive_data) if np.mean(positive_data) > 0 else 0
        else:
            cv = 0
        
        cv_values.append(cv)
        cv_timestamps.append(timestamps[i])
    
    # 计算变异系数差分
    if len(cv_values) > 1:
        diff_values = np.diff(cv_values)
        diff_timestamps = cv_timestamps[1:]
        
        if len(diff_values) > 0:
            diff_std = np.std(diff_values)
            threshold_1 = diff_std * cv_threshold
            threshold_2 = diff_std * fixed_threshold
            
            # 找出满足三重条件的候选间断点
            candidate_breakpoints = []
            
            for i in range(len(diff_values)):
                if (abs(diff_values[i]) > threshold_1 and 
                    abs(diff_values[i]) > threshold_2 and 
                    abs(diff_values[i]) > fixed_threshold):
                    candidate_breakpoints.append(diff_timestamps[i])
            
            # 应用最小距离过滤
            if candidate_breakpoints:
                filtered_breakpoints = [candidate_breakpoints[0]]
                
                for bp in candidate_breakpoints[1:]:
                    # 计算与最后一个间断点的时间差（小时）
                    time_diff = (bp - filtered_breakpoints[-1]).total_seconds() / 3600
                    if time_diff >= min_distance:
                        filtered_breakpoints.append(bp)
                
                # 限制最大间断点数量为7个
                breakpoints = filtered_breakpoints[:7]
    
    return breakpoints

def classify_segment_status_with_config(flow_data, config):
    """
    使用配置参数进行时间段运行状态分类
    
    Args:
        flow_data: 流量数据序列
        config: 配置参数字典
    
    Returns:
        dict: 状态分类结果
    """
    total_count = len(flow_data)
    if total_count == 0:
        return {'status': '无数据', 'confidence': 0.0}
    
    # 计算零值或极低值比例
    low_value_count = (flow_data <= config['low_value_limit']).sum()
    low_value_ratio = low_value_count / total_count
    
    # 1. 停运状态：零值或极低值比例 ≥ 配置阈值
    if low_value_ratio >= config['shutdown_threshold']:
        return {
            'status': '停运状态',
            'confidence': min(0.95, 0.5 + low_value_ratio),
            'low_value_ratio': low_value_ratio,
            'description': f'零值或极低值比例{low_value_ratio:.1%}，判定为停运状态'
        }
    
    # 2. 对于非停运状态，计算变异系数
    positive_data = flow_data[flow_data > config['low_value_limit']]
    if len(positive_data) < 3:
        return {
            'status': '停运状态',
            'confidence': 0.8,
            'low_value_ratio': low_value_ratio,
            'description': '有效数据点过少，判定为停运状态'
        }
    
    mean_val = np.mean(positive_data)
    std_val = np.std(positive_data)
    cv = std_val / mean_val if mean_val > 0 else 0
    
    # 3. 单状态运行：变异系数 < 配置阈值
    if cv < config['single_state_cv_max']:
        return {
            'status': '单状态运行',
            'confidence': min(0.95, 0.5 + (1 - cv)),
            'cv': cv,
            'low_value_ratio': low_value_ratio,
            'description': f'变异系数{cv:.3f}，数值相对稳定，判定为单状态运行'
        }
    
    # 4. 正常波动：变异系数 ≥ 配置阈值
    else:
        return {
            'status': '正常波动',
            'confidence': min(0.95, 0.5 + min(cv, 1.0)),
            'cv': cv,
            'low_value_ratio': low_value_ratio,
            'description': f'变异系数{cv:.3f}，存在明显变化，判定为正常波动'
        }

def calculate_protection_interval_with_config(flow_data, config):
    """
    使用配置参数计算区间保护机制的保护区间
    
    Args:
        flow_data: 流量数据序列
        config: 配置参数字典
    
    Returns:
        dict: 保护区间信息
    """
    # 过滤负值，只对正值数据计算保护区间
    positive_data = flow_data[flow_data > 0]
    
    if len(positive_data) < 3:
        return {
            'lower': 0,
            'upper': float('inf'),
            'median': 0,
            'std': 0,
            'adjustment_factor': 1.0,
            'description': '数据不足，无法计算保护区间'
        }
    
    median_val = np.median(positive_data)
    std_val = np.std(positive_data)
    
    # 根据标准差大小反向调整系数
    if std_val < config['protection_std_small']:
        adjustment_factor = config['protection_factor_small']  # 标准差小，保护区间扩大
    elif std_val < config['protection_std_medium']:
        adjustment_factor = config['protection_factor_medium']  # 标准差中等，正常保护区间
    else:
        adjustment_factor = config['protection_factor_large']  # 标准差大，保护区间缩小
    
    # 计算保护区间
    protection_range = std_val * adjustment_factor
    lower_bound = max(0, median_val - protection_range)
    upper_bound = median_val + protection_range
    
    return {
        'lower': lower_bound,
        'upper': upper_bound,
        'median': median_val,
        'std': std_val,
        'adjustment_factor': adjustment_factor,
        'description': f'保护区间: [{lower_bound:.3f}, {upper_bound:.3f}]'
    }

def detect_segment_anomalies_with_config(segment_data, segment_status, config):
    """
    使用配置参数进行时间段异常检测
    
    Args:
        segment_data: 时间段数据
        segment_status: 时间段状态
        config: 配置参数字典
    
    Returns:
        dict: 异常检测结果
    """
    flow_values = segment_data['flow_value'].values
    timestamps = segment_data['timestamp'].values

    if len(flow_values) < 3:
        return {
            'anomaly_indices': [],
            'anomaly_scores': {},
            'methods_used': [],
            'total_anomalies': 0
        }

    # 业务规则层：负值异常检测
    negative_anomalies = []
    for i, val in enumerate(flow_values):
        if val < 0:
            negative_anomalies.append(i)

    # 统计方法层：使用配置选择的方法
    statistical_anomalies = []
    anomaly_scores = {}
    methods_used = []

    # 过滤正值数据用于统计计算
    positive_mask = flow_values > 0
    if np.sum(positive_mask) >= 3:
        positive_values = flow_values[positive_mask]
        positive_indices = np.where(positive_mask)[0]

        # 1. P5/P95百分位数阈值法
        if config['use_p5_p95']:
            try:
                p5 = np.percentile(positive_values, 5)
                p95 = np.percentile(positive_values, 95)
                p5_p95_anomalies = []
                for i, val in enumerate(flow_values):
                    if val > 0 and (val < p5 or val > p95):
                        p5_p95_anomalies.append(i)
                        anomaly_scores[i] = anomaly_scores.get(i, 0) + config['p5_p95_weight']
                methods_used.append('P5/P95')
            except:
                p5_p95_anomalies = []

        # 2. IQR四分位距法
        if config['use_iqr']:
            try:
                q1 = np.percentile(positive_values, 25)
                q3 = np.percentile(positive_values, 75)
                iqr = q3 - q1
                iqr_lower = q1 - config['iqr_multiplier'] * iqr
                iqr_upper = q3 + config['iqr_multiplier'] * iqr
                iqr_anomalies = []
                for i, val in enumerate(flow_values):
                    if val > 0 and (val < iqr_lower or val > iqr_upper):
                        iqr_anomalies.append(i)
                        anomaly_scores[i] = anomaly_scores.get(i, 0) + config['iqr_weight']
                methods_used.append('IQR')
            except:
                iqr_anomalies = []

        # 3. MAD中位数绝对偏差法
        if config['use_mad']:
            try:
                median_val = np.median(positive_values)
                mad = np.median(np.abs(positive_values - median_val))
                if mad > 0:
                    mad_lower = median_val - config['mad_multiplier'] * mad
                    mad_upper = median_val + config['mad_multiplier'] * mad
                    mad_anomalies = []
                    for i, val in enumerate(flow_values):
                        if val > 0 and (val < mad_lower or val > mad_upper):
                            mad_anomalies.append(i)
                            anomaly_scores[i] = anomaly_scores.get(i, 0) + config['mad_weight']
                    methods_used.append('MAD')
                else:
                    mad_anomalies = []
            except:
                mad_anomalies = []

        # 合并统计异常
        all_statistical = []
        if config['use_p5_p95']:
            all_statistical.extend(p5_p95_anomalies)
        if config['use_iqr']:
            all_statistical.extend(iqr_anomalies)
        if config['use_mad']:
            all_statistical.extend(mad_anomalies)
        
        statistical_anomalies = list(set(all_statistical))

    # 应用区间保护机制
    protection_interval = calculate_protection_interval_with_config(pd.Series(flow_values), config)
    protected_anomalies = []

    for idx in statistical_anomalies:
        val = flow_values[idx]
        # 如果值在保护区间内且为正值，则不判定为异常
        if val > 0 and protection_interval['lower'] <= val <= protection_interval['upper']:
            continue  # 受保护，不判定为异常
        else:
            protected_anomalies.append(idx)

    # 合并所有异常
    all_anomalies = list(set(negative_anomalies + protected_anomalies))

    # 为负值异常添加高分
    for idx in negative_anomalies:
        anomaly_scores[idx] = anomaly_scores.get(idx, 0) + config['negative_weight']

    return {
        'anomaly_indices': sorted(all_anomalies),
        'anomaly_scores': anomaly_scores,
        'methods_used': methods_used,
        'total_anomalies': len(all_anomalies),
        'negative_anomalies': negative_anomalies,
        'statistical_anomalies': protected_anomalies,
        'protection_interval': protection_interval
    }

def perform_time_segment_analysis_with_config(system, config, progress_callback=None):
    """
    使用配置参数执行基于时间段的分析

    Args:
        system: CityAnomalyDetectionSystem实例
        config: 配置参数字典
        progress_callback: 进度回调函数

    Returns:
        dict: 分析结果
    """
    if progress_callback:
        progress_callback("执行基于时间段的分析")

    segment_results = {}

    for month in system.monthly_data.keys():
        if progress_callback:
            progress_callback(f"处理{month}月数据...")

        month_data = system.monthly_data[month]
        month_results = {}

        # 按站点分组处理
        for (company_name, site_name), site_data in month_data.groupby(['company_name', 'site_name']):
            if len(site_data) < 10:  # 数据量太少跳过
                continue

            # 按时间排序
            site_data_sorted = site_data.sort_values('timestamp')

            # 检测间断点
            breakpoints = detect_breakpoints_with_config(
                site_data_sorted['flow_value'].values,
                site_data_sorted['timestamp'].values,
                config
            )

            # 划分时间段
            segments = divide_into_segments_with_config(site_data_sorted, breakpoints, config)

            month_results[(company_name, site_name)] = {
                'data': site_data_sorted,
                'breakpoints': breakpoints,
                'segments': segments,
                'total_segments': len(segments)
            }

        segment_results[month] = month_results

    return segment_results

def divide_into_segments_with_config(site_data, breakpoints, config):
    """
    使用配置参数将数据划分为时间段

    Args:
        site_data: 站点数据
        breakpoints: 间断点列表
        config: 配置参数字典

    Returns:
        list: 时间段列表
    """
    segments = []

    # 创建时间段边界
    boundaries = [site_data['timestamp'].min()] + breakpoints + [site_data['timestamp'].max()]

    for i in range(len(boundaries) - 1):
        start_time = boundaries[i]
        end_time = boundaries[i + 1]

        # 提取该时间段的数据
        segment_data = site_data[
            (site_data['timestamp'] >= start_time) &
            (site_data['timestamp'] <= end_time)
        ].copy()

        if len(segment_data) == 0:
            continue

        # 判定该时间段的运行状态
        segment_status = classify_segment_status_with_config(segment_data['flow_value'], config)

        # 检测该时间段的异常
        segment_anomalies = detect_segment_anomalies_with_config(segment_data, segment_status, config)

        # 计算保护区间
        protection_interval = calculate_protection_interval_with_config(segment_data['flow_value'], config)

        segments.append({
            'segment_id': i,
            'start_time': start_time,
            'end_time': end_time,
            'data_count': len(segment_data),
            'status': segment_status,
            'anomalies': segment_anomalies,
            'protection_interval': protection_interval,
            'data': segment_data
        })

    return segments

def generate_time_segment_visualizations_with_config(system, segment_results, output_dir, config, progress_callback=None):
    """
    使用配置参数生成时间段可视化图表

    Args:
        system: CityAnomalyDetectionSystem实例
        segment_results: 分析结果
        output_dir: 输出目录
        config: 配置参数字典
        progress_callback: 进度回调函数

    Returns:
        dict: 图表生成结果
    """
    if progress_callback:
        progress_callback("生成可视化图表...")

    chart_results = {
        'total_charts': 0,
        'output_dir': output_dir,
        'chart_files': []
    }

    for month, month_results in segment_results.items():
        if progress_callback:
            progress_callback(f"生成{month}月图表...")

        for (company_name, site_name), site_results in month_results.items():
            try:
                # 生成散点图
                chart_file = generate_single_scatter_plot_with_config(
                    company_name, site_name, month,
                    site_results, output_dir, config
                )

                if chart_file:
                    chart_results['chart_files'].append(chart_file)
                    chart_results['total_charts'] += 1

            except Exception as e:
                if progress_callback:
                    progress_callback(f"生成图表失败: {company_name}-{site_name}: {str(e)}")

    return chart_results

def generate_single_scatter_plot_with_config(company_name, site_name, month, site_results, output_dir, config):
    """
    使用配置参数生成单个散点图

    Args:
        company_name: 企业名称
        site_name: 站点名称
        month: 月份
        site_results: 站点分析结果
        output_dir: 输出目录
        config: 配置参数字典

    Returns:
        str: 生成的图表文件路径
    """
    try:
        site_data = site_results['data']
        segments = site_results['segments']
        breakpoints = site_results['breakpoints']

        # 创建图表
        fig, ax = plt.subplots(figsize=(16, 10))

        # 收集所有异常索引
        all_anomaly_indices = set()
        all_negative_indices = set()
        all_significant_indices = set()

        for segment in segments:
            anomalies = segment['anomalies']
            segment_data = segment['data']

            # 将段内索引转换为全局索引
            global_indices = segment_data.index.tolist()

            for local_idx in anomalies['anomaly_indices']:
                if local_idx < len(global_indices):
                    global_idx = global_indices[local_idx]
                    all_anomaly_indices.add(global_idx)

                    # 检查是否为负值异常
                    if local_idx in anomalies['negative_anomalies']:
                        all_negative_indices.add(global_idx)

                    # 检查是否为显著异常（高评分）
                    score = anomalies['anomaly_scores'].get(local_idx, 0)
                    if score >= 6:  # 高评分阈值
                        all_significant_indices.add(global_idx)

        # 准备数据
        timestamps = site_data['timestamp'].values
        flow_values = site_data['flow_value'].values

        # 分类数据点
        normal_indices = []
        zero_indices = []

        for i in range(len(flow_values)):
            idx = site_data.index[i]
            if idx not in all_anomaly_indices:
                if flow_values[i] == 0:
                    zero_indices.append(i)
                else:
                    normal_indices.append(i)

        # 绘制数据点（五色编码系统）
        # 1. 正常数值（蓝色圆点）
        if normal_indices:
            ax.scatter([timestamps[i] for i in normal_indices],
                      [flow_values[i] for i in normal_indices],
                      c='blue', alpha=0.7, s=20, label='正常数值', marker='o')

        # 2. 零值（绿色圆点）
        if zero_indices:
            ax.scatter([timestamps[i] for i in zero_indices],
                      [flow_values[i] for i in zero_indices],
                      c='green', alpha=0.7, s=20, label='零值', marker='o')

        # 3. 负值异常（黑色叉号）
        negative_plot_indices = [i for i in range(len(flow_values))
                               if site_data.index[i] in all_negative_indices]
        if negative_plot_indices:
            ax.scatter([timestamps[i] for i in negative_plot_indices],
                      [flow_values[i] for i in negative_plot_indices],
                      c='black', alpha=0.9, s=25, label='负值异常', marker='x', linewidth=1.5)

        # 4. 统计异常（黄色圆点）
        statistical_indices = [i for i in range(len(flow_values))
                             if site_data.index[i] in all_anomaly_indices
                             and site_data.index[i] not in all_negative_indices
                             and site_data.index[i] not in all_significant_indices]
        if statistical_indices:
            ax.scatter([timestamps[i] for i in statistical_indices],
                      [flow_values[i] for i in statistical_indices],
                      c='yellow', alpha=0.8, s=30, label='统计异常', marker='o')

        # 5. 显著异常（红色圆点）
        significant_plot_indices = [i for i in range(len(flow_values))
                                  if site_data.index[i] in all_significant_indices]
        if significant_plot_indices:
            ax.scatter([timestamps[i] for i in significant_plot_indices],
                      [flow_values[i] for i in significant_plot_indices],
                      c='red', alpha=0.9, s=35, label='显著异常', marker='o')

        # 绘制间断点（绿色虚线）
        for bp_time in breakpoints:
            ax.axvline(x=bp_time, color='green', linestyle='--', alpha=0.7, linewidth=1.5, label='间断点')

        # 添加时间段状态标注
        y_max = max(flow_values) if len(flow_values) > 0 else 1
        for i, segment in enumerate(segments):
            start_time = segment['start_time']
            end_time = segment['end_time']
            mid_time = start_time + (end_time - start_time) / 2

            status = segment['status']['status']
            confidence = segment['status']['confidence']

            y_text_pos = y_max * 1.1
            ax.text(mid_time, y_text_pos - (i % 3) * y_max * 0.1,
                   f"段{i+1}: {status}\n(置信度:{confidence:.2f})",
                   ha='center', va='top', fontsize=10,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7))

        # 设置图表属性
        ax.set_xlabel('时间', fontsize=14)
        ax.set_ylabel('流量值', fontsize=14)
        ax.set_title(f'{company_name}-{site_name}-{month}月', fontsize=16, fontweight='bold')

        # 设置时间轴格式
        ax.xaxis.set_major_formatter(DateFormatter('%m-%d %H:%M'))
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=6))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        # 设置纵坐标范围
        if len(flow_values) > 0:
            y_min = min(flow_values)
            y_max = max(flow_values)
            y_range = y_max - y_min
            if y_range > 0:
                ax.set_ylim(y_min - y_range * 0.05, y_max + y_range * 0.15)
            else:
                ax.set_ylim(y_min - 1, y_max + 1)

        ax.legend(fontsize=10)
        ax.grid(True, alpha=0.3)

        # 保存图表
        safe_company = company_name.replace('/', '_').replace('\\', '_')[:15]
        safe_site = site_name.replace('/', '_').replace('\\', '_')[:15]
        filename = f"{safe_company}_{safe_site}_{month}月_时间段分析.png"
        filepath = os.path.join(output_dir, filename)

        plt.tight_layout()
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        return filepath

    except Exception as e:
        plt.close()
        raise e
