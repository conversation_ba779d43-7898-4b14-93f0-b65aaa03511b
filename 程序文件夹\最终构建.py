#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版本的可执行文件构建脚本
移除不必要的sklearn依赖
"""

import os
import sys
import subprocess
import shutil

def install_pyinstaller():
    """安装PyInstaller"""
    print("检查PyInstaller...")
    try:
        import PyInstaller
        print("✓ PyInstaller已安装")
        return True
    except ImportError:
        print("正在安装PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✓ PyInstaller安装成功")
            return True
        except:
            print("✗ PyInstaller安装失败")
            return False

def build_final_exe():
    """构建最终版可执行文件"""
    print("开始构建最终版可执行文件...")
    
    # 移除sklearn依赖，只保留必要的模块
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",  # 单文件模式
        "--console",  # 使用控制台模式
        "--name=污染源异常检测系统V6.0_最终版",
        "--add-data=V6_0核心算法模块.py;.",
        "--add-data=污染源异常检测GUI系统.py;.",
        "--add-data=../城市在线监测流量异常检测系统/城市污染源异常检测系统.py;.",
        # 只包含必要的模块
        "--hidden-import=tkinter",
        "--hidden-import=tkinter.ttk",
        "--hidden-import=tkinter.filedialog",
        "--hidden-import=tkinter.messagebox",
        "--hidden-import=tkinter.scrolledtext",
        "--hidden-import=pandas",
        "--hidden-import=numpy",
        "--hidden-import=matplotlib",
        "--hidden-import=matplotlib.pyplot",
        "--hidden-import=matplotlib.backends.backend_tkagg",
        "--hidden-import=matplotlib.dates",
        "--hidden-import=openpyxl",
        "--hidden-import=xlrd",
        "--hidden-import=datetime",
        "--hidden-import=threading",
        "--hidden-import=json",
        # 收集matplotlib相关文件
        "--collect-all=matplotlib",
        "--collect-all=tkinter",
        # 排除不需要的模块
        "--exclude-module=sklearn",
        "--exclude-module=scikit-learn",
        "--exclude-module=scipy",
        "--exclude-module=joblib",
        "--exclude-module=threadpoolctl",
        "启动GUI系统.py"
    ]
    
    try:
        print("执行PyInstaller命令...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 构建成功")
            return True
        else:
            print("✗ 构建失败")
            print("错误信息:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 构建异常: {str(e)}")
        return False

def copy_final_exe():
    """复制最终版可执行文件"""
    source = "dist/污染源异常检测系统V6.0_最终版.exe"
    target = "污染源异常检测系统V6.0_最终版.exe"
    
    if os.path.exists(source):
        try:
            shutil.copy2(source, target)
            size_mb = os.path.getsize(target) / (1024 * 1024)
            print(f"✓ 最终版可执行文件已生成: {target} ({size_mb:.1f} MB)")
            return True
        except Exception as e:
            print(f"✗ 复制文件失败: {str(e)}")
            return False
    else:
        print(f"✗ 找不到生成的文件: {source}")
        return False

def create_final_batch():
    """创建最终版启动批处理文件"""
    batch_content = '''@echo off
title 污染源异常检测系统V6.0
echo ===============================================
echo 污染源流量异常检测系统 V6.0 - 最终版
echo ===============================================
echo.
echo 正在启动程序，请稍候...
echo.
echo 注意事项：
echo 1. 程序启动后会显示GUI界面
echo 2. 请不要关闭此控制台窗口
echo 3. 如有错误信息会在此窗口显示
echo.
echo ===============================================
echo.

"污染源异常检测系统V6.0_最终版.exe"

echo.
echo ===============================================
echo 程序已退出
echo ===============================================
pause
'''
    
    try:
        with open("启动最终版.bat", "w", encoding="gbk") as f:
            f.write(batch_content)
        print("✓ 最终版启动批处理文件创建成功: 启动最终版.bat")
        return True
    except Exception as e:
        print(f"✗ 创建批处理文件失败: {str(e)}")
        return False

def cleanup_final():
    """清理临时文件"""
    print("清理临时文件...")
    dirs_to_remove = ['build', 'dist', '__pycache__']
    files_to_remove = ['污染源异常检测系统V6.0_最终版.spec']
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✓ 删除: {dir_name}")
            except:
                print(f"⚠️ 无法删除: {dir_name}")
    
    for file_name in files_to_remove:
        if os.path.exists(file_name):
            try:
                os.remove(file_name)
                print(f"✓ 删除: {file_name}")
            except:
                print(f"⚠️ 无法删除: {file_name}")

def create_final_readme():
    """创建最终版使用说明"""
    readme_content = """# 污染源异常检测系统V6.0 - 最终版使用说明

## 版本特点
- 移除了不必要的sklearn依赖，减小文件体积
- 使用控制台模式启动，提高稳定性
- 完整保留V6.0系统的所有核心功能

## 启动方法
推荐方法：双击 "启动最终版.bat"
备用方法：双击 "污染源异常检测系统V6.0_最终版.exe"

## 系统要求
- Windows 7/8/10/11 (64位)
- 至少 4GB 内存
- 至少 300MB 可用磁盘空间

## 使用步骤
1. 双击启动批处理文件
2. 等待GUI界面出现
3. 在"数据输入"选项卡选择Excel文件
4. 在"参数配置"选项卡调整参数（可选）
5. 在"程序执行"选项卡开始分析
6. 查看生成的报告和图表

## 数据文件要求
Excel文件必须包含以下列：
- 企业名称
- 监测点名称
- data_time（时间）
- 流量

## 输出结果
- Excel格式分析报告
- PNG格式散点图
- 时间段分析汇总

## 注意事项
1. 启动时会显示控制台窗口，这是正常现象
2. 请不要关闭控制台窗口
3. 如有错误，控制台会显示详细信息
4. 分析过程中请耐心等待

## 故障排除
如果程序无法启动：
1. 检查系统是否为64位Windows
2. 确认有足够的磁盘空间
3. 尝试以管理员身份运行
4. 检查杀毒软件是否误报

最终版生成时间：2025-07-31
技术支持：Augment Agent
"""
    
    try:
        with open("最终版使用说明.txt", "w", encoding="utf-8") as f:
            f.write(readme_content)
        print("✓ 最终版使用说明创建成功")
        return True
    except Exception as e:
        print(f"✗ 创建使用说明失败: {str(e)}")
        return False

def test_dependencies():
    """测试必要依赖"""
    print("测试必要依赖...")
    
    required_modules = [
        'tkinter',
        'pandas', 
        'numpy',
        'matplotlib',
        'openpyxl'
    ]
    
    missing = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ {module}")
        except ImportError:
            print(f"✗ {module}")
            missing.append(module)
    
    if missing:
        print(f"缺少模块: {missing}")
        return False
    
    print("✓ 所有必要依赖都已安装")
    return True

def main():
    """主函数"""
    print("污染源异常检测系统V6.0 - 最终版构建工具")
    print("=" * 60)
    
    # 检查必要文件
    required_files = [
        "启动GUI系统.py",
        "污染源异常检测GUI系统.py", 
        "V6_0核心算法模块.py"
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"✗ 缺少必要文件: {missing_files}")
        return False
    
    print("✓ 必要文件检查通过")
    
    # 构建步骤
    steps = [
        ("测试依赖模块", test_dependencies),
        ("安装PyInstaller", install_pyinstaller),
        ("构建最终版exe", build_final_exe),
        ("复制可执行文件", copy_final_exe),
        ("创建启动批处理", create_final_batch),
        ("创建使用说明", create_final_readme),
        ("清理临时文件", cleanup_final)
    ]
    
    for step_name, step_func in steps:
        print(f"\n--- {step_name} ---")
        if not step_func():
            print(f"❌ {step_name}失败")
            return False
    
    print("\n" + "=" * 60)
    print("🎉 最终版构建完成！")
    print("\n生成的文件:")
    print("- 污染源异常检测系统V6.0_最终版.exe (主程序)")
    print("- 启动最终版.bat (推荐启动方式)")
    print("- 最终版使用说明.txt (详细说明)")
    print("\n✨ 这个版本移除了不必要的依赖，应该可以正常运行了！")
    print("建议使用 '启动最终版.bat' 启动程序。")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n构建失败")
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"\n异常: {str(e)}")
        input("\n按回车键退出...")
