#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试exe文件运行状态的脚本
"""

import os
import sys
import subprocess
import time
import psutil

def check_exe_exists():
    """检查exe文件是否存在"""
    exe_path = "污染源异常检测系统V6.0.exe"
    if os.path.exists(exe_path):
        size_mb = os.path.getsize(exe_path) / (1024 * 1024)
        print(f"✓ exe文件存在: {exe_path}")
        print(f"✓ 文件大小: {size_mb:.1f} MB")
        return True
    else:
        print(f"✗ exe文件不存在: {exe_path}")
        return False

def test_exe_launch():
    """测试exe文件启动"""
    exe_path = "污染源异常检测系统V6.0.exe"
    
    print("尝试启动exe文件...")
    
    try:
        # 启动进程但不等待
        process = subprocess.Popen([exe_path], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 creationflags=subprocess.CREATE_NEW_CONSOLE)
        
        print(f"✓ 进程已启动，PID: {process.pid}")
        
        # 等待几秒钟看是否有立即的错误
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✓ 进程仍在运行，程序启动成功")
            
            # 尝试终止进程
            try:
                process.terminate()
                process.wait(timeout=5)
                print("✓ 进程已正常终止")
            except subprocess.TimeoutExpired:
                process.kill()
                print("⚠️ 进程被强制终止")
            
            return True
        else:
            # 进程已退出，检查错误
            stdout, stderr = process.communicate()
            print(f"✗ 进程已退出，返回码: {process.returncode}")
            
            if stderr:
                print("错误输出:")
                print(stderr.decode('utf-8', errors='ignore'))
            
            if stdout:
                print("标准输出:")
                print(stdout.decode('utf-8', errors='ignore'))
            
            return False
            
    except Exception as e:
        print(f"✗ 启动exe失败: {str(e)}")
        return False

def check_dependencies():
    """检查系统依赖"""
    print("检查系统依赖...")
    
    # 检查是否有必要的DLL文件
    system_dlls = [
        'kernel32.dll',
        'user32.dll',
        'gdi32.dll'
    ]
    
    for dll in system_dlls:
        try:
            import ctypes
            ctypes.windll.LoadLibrary(dll)
            print(f"✓ {dll} 可用")
        except:
            print(f"✗ {dll} 不可用")

def check_running_processes():
    """检查是否有相关进程在运行"""
    print("检查相关进程...")
    
    target_names = ['污染源异常检测系统V6.0.exe', 'python.exe']
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            proc_info = proc.info
            if any(target in str(proc_info.get('name', '')) or 
                   target in str(proc_info.get('cmdline', '')) 
                   for target in target_names):
                print(f"发现相关进程: PID={proc_info['pid']}, Name={proc_info['name']}")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue

def test_file_permissions():
    """测试文件权限"""
    exe_path = "污染源异常检测系统V6.0.exe"
    
    print("检查文件权限...")
    
    try:
        # 检查读权限
        if os.access(exe_path, os.R_OK):
            print("✓ 文件可读")
        else:
            print("✗ 文件不可读")
            
        # 检查执行权限
        if os.access(exe_path, os.X_OK):
            print("✓ 文件可执行")
        else:
            print("✗ 文件不可执行")
            
        return True
        
    except Exception as e:
        print(f"✗ 权限检查失败: {str(e)}")
        return False

def create_test_data():
    """创建测试数据文件"""
    print("创建测试数据文件...")
    
    try:
        import pandas as pd
        from datetime import datetime, timedelta
        import numpy as np
        
        # 创建测试数据
        start_time = datetime.now() - timedelta(days=1)
        timestamps = [start_time + timedelta(hours=i) for i in range(100)]
        
        test_data = {
            '企业名称': ['测试企业'] * 100,
            '监测点名称': ['测试监测点'] * 100,
            'data_time': timestamps,
            '流量': np.random.normal(10, 2, 100),
            '数据月份': [start_time.month] * 100
        }
        
        df = pd.DataFrame(test_data)
        test_file = "测试数据.xlsx"
        df.to_excel(test_file, index=False)
        
        print(f"✓ 测试数据文件创建成功: {test_file}")
        return test_file
        
    except Exception as e:
        print(f"✗ 创建测试数据失败: {str(e)}")
        return None

def main():
    """主测试函数"""
    print("污染源异常检测系统V6.0 - exe文件运行测试")
    print("=" * 60)
    
    tests = [
        ("检查exe文件存在", check_exe_exists),
        ("检查文件权限", test_file_permissions),
        ("检查系统依赖", check_dependencies),
        ("检查运行进程", check_running_processes),
        ("测试exe启动", test_exe_launch),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}异常: {str(e)}")
            results.append((test_name, False))
    
    # 创建测试数据
    print(f"\n--- 创建测试数据 ---")
    test_file = create_test_data()
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 exe文件测试全部通过！")
        print("\n建议:")
        print("1. 双击exe文件手动测试GUI界面")
        if test_file:
            print(f"2. 使用生成的测试数据文件: {test_file}")
        print("3. 验证数据分析功能是否正常")
    else:
        print("⚠️ 部分测试失败，exe可能存在问题")
        print("\n建议:")
        print("1. 检查系统环境和依赖")
        print("2. 尝试重新构建exe文件")
        print("3. 检查源代码是否完整")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
