#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI系统测试脚本
测试污染源异常检测GUI系统的各个功能模块
"""

import sys
import os
import traceback

def test_imports():
    """测试导入模块"""
    print("=== 测试导入模块 ===")
    
    try:
        # 测试基础模块导入
        import tkinter as tk
        from tkinter import ttk
        print("✓ tkinter模块导入成功")
        
        import pandas as pd
        import numpy as np
        print("✓ pandas和numpy模块导入成功")
        
        import matplotlib.pyplot as plt
        print("✓ matplotlib模块导入成功")
        
        # 测试V6.0核心算法模块导入
        from V6_0核心算法模块 import (
            perform_time_segment_analysis_with_config,
            generate_time_segment_visualizations_with_config
        )
        print("✓ V6.0核心算法模块导入成功")
        
        # 测试城市污染源异常检测系统导入
        sys.path.append('..')
        sys.path.append('../城市在线监测流量异常检测系统')
        from 城市污染源异常检测系统 import CityAnomalyDetectionSystem
        print("✓ 城市污染源异常检测系统导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入模块失败: {str(e)}")
        traceback.print_exc()
        return False

def test_gui_creation():
    """测试GUI创建"""
    print("\n=== 测试GUI创建 ===")
    
    try:
        # 导入GUI系统
        from 污染源异常检测GUI系统 import PollutionDetectionGUI
        
        # 创建测试窗口
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口，避免显示
        
        # 创建GUI应用
        app = PollutionDetectionGUI(root)
        print("✓ GUI应用创建成功")
        
        # 测试配置参数
        if hasattr(app, 'config') and isinstance(app.config, dict):
            print(f"✓ 配置参数加载成功，共 {len(app.config)} 个参数")
            
            # 检查关键参数
            key_params = ['window_size', 'cv_threshold', 'fixed_threshold', 'shutdown_threshold']
            for param in key_params:
                if param in app.config:
                    print(f"  - {param}: {app.config[param]}")
                else:
                    print(f"  ✗ 缺少关键参数: {param}")
        else:
            print("✗ 配置参数加载失败")
        
        # 测试界面组件
        if hasattr(app, 'notebook'):
            tab_count = app.notebook.index("end")
            print(f"✓ 选项卡创建成功，共 {tab_count} 个选项卡")
        else:
            print("✗ 选项卡创建失败")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ GUI创建失败: {str(e)}")
        traceback.print_exc()
        return False

def test_config_parameters():
    """测试配置参数"""
    print("\n=== 测试配置参数 ===")
    
    try:
        from 污染源异常检测GUI系统 import PollutionDetectionGUI
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = PollutionDetectionGUI(root)
        
        # 测试参数更新
        original_value = app.config['window_size']
        app.update_config('window_size', 48)
        
        if app.config['window_size'] == 48:
            print("✓ 参数更新功能正常")
            app.config['window_size'] = original_value  # 恢复原值
        else:
            print("✗ 参数更新功能异常")
        
        # 测试必要参数存在性
        required_params = [
            'window_size', 'cv_threshold', 'fixed_threshold', 'min_distance',
            'shutdown_threshold', 'low_value_limit', 'single_state_cv_max',
            'use_p5_p95', 'use_iqr', 'use_mad',
            'p5_p95_weight', 'iqr_weight', 'mad_weight', 'negative_weight',
            'mad_multiplier', 'iqr_multiplier'
        ]
        
        missing_params = []
        for param in required_params:
            if param not in app.config:
                missing_params.append(param)
        
        if not missing_params:
            print("✓ 所有必要参数都存在")
        else:
            print(f"✗ 缺少参数: {missing_params}")
        
        root.destroy()
        return len(missing_params) == 0
        
    except Exception as e:
        print(f"✗ 配置参数测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_core_algorithm():
    """测试核心算法"""
    print("\n=== 测试核心算法 ===")
    
    try:
        from V6_0核心算法模块 import (
            detect_breakpoints_with_config,
            classify_segment_status_with_config,
            calculate_protection_interval_with_config,
            detect_segment_anomalies_with_config
        )
        
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta
        
        # 创建测试数据
        timestamps = [datetime.now() + timedelta(hours=i) for i in range(100)]
        flow_data = np.random.normal(10, 2, 100)  # 正态分布数据
        flow_data[20:30] = 0  # 添加一些零值
        flow_data[50] = -1  # 添加负值
        flow_data[80:90] = np.random.normal(20, 1, 10)  # 添加异常高值
        
        # 测试配置
        test_config = {
            'window_size': 24,
            'cv_threshold': 0.8,
            'fixed_threshold': 0.5,
            'min_distance': 12,
            'shutdown_threshold': 0.7,
            'low_value_limit': 0.2,
            'single_state_cv_max': 0.3,
            'protection_std_small': 0.1,
            'protection_std_medium': 0.5,
            'protection_factor_small': 1.5,
            'protection_factor_medium': 1.0,
            'protection_factor_large': 0.5,
            'use_p5_p95': True,
            'use_iqr': True,
            'use_mad': True,
            'p5_p95_weight': 3,
            'iqr_weight': 3,
            'mad_weight': 3,
            'negative_weight': 5,
            'mad_multiplier': 2.5,
            'iqr_multiplier': 1.5
        }
        
        # 测试间断点检测
        breakpoints = detect_breakpoints_with_config(flow_data, timestamps, test_config)
        print(f"✓ 间断点检测完成，检测到 {len(breakpoints)} 个间断点")
        
        # 测试状态分类
        status = classify_segment_status_with_config(pd.Series(flow_data), test_config)
        print(f"✓ 状态分类完成，状态: {status['status']}, 置信度: {status['confidence']:.3f}")
        
        # 测试保护区间计算
        protection = calculate_protection_interval_with_config(pd.Series(flow_data), test_config)
        print(f"✓ 保护区间计算完成: [{protection['lower']:.3f}, {protection['upper']:.3f}]")
        
        # 测试异常检测
        test_segment_data = pd.DataFrame({
            'timestamp': timestamps,
            'flow_value': flow_data
        })
        
        anomalies = detect_segment_anomalies_with_config(test_segment_data, status, test_config)
        print(f"✓ 异常检测完成，检测到 {anomalies['total_anomalies']} 个异常")
        print(f"  - 使用方法: {anomalies['methods_used']}")
        print(f"  - 负值异常: {len(anomalies['negative_anomalies'])} 个")
        print(f"  - 统计异常: {len(anomalies['statistical_anomalies'])} 个")
        
        return True
        
    except Exception as e:
        print(f"✗ 核心算法测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_data_file_access():
    """测试数据文件访问"""
    print("\n=== 测试数据文件访问 ===")
    
    try:
        # 检查数据读取文件夹
        data_folder = "../数据读取"
        if os.path.exists(data_folder):
            print(f"✓ 数据文件夹存在: {data_folder}")
            
            # 列出可用的数据文件
            data_files = [f for f in os.listdir(data_folder) if f.endswith('.xlsx')]
            print(f"✓ 找到 {len(data_files)} 个Excel数据文件")
            
            for file in data_files[:3]:  # 只显示前3个文件
                print(f"  - {file}")
            
            if len(data_files) > 3:
                print(f"  ... 还有 {len(data_files) - 3} 个文件")
            
            return len(data_files) > 0
        else:
            print(f"✗ 数据文件夹不存在: {data_folder}")
            return False
            
    except Exception as e:
        print(f"✗ 数据文件访问测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("污染源异常检测GUI系统 - 功能测试")
    print("=" * 50)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("导入模块", test_imports()))
    test_results.append(("GUI创建", test_gui_creation()))
    test_results.append(("配置参数", test_config_parameters()))
    test_results.append(("核心算法", test_core_algorithm()))
    test_results.append(("数据文件访问", test_data_file_access()))
    
    # 汇总测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！GUI系统准备就绪。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
