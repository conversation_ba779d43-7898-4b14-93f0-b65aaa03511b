#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复后的可执行文件构建脚本
解决stdin/stdout问题
"""

import os
import sys
import subprocess
import shutil

def install_pyinstaller():
    """安装PyInstaller"""
    print("检查PyInstaller...")
    try:
        import PyInstaller
        print("✓ PyInstaller已安装")
        return True
    except ImportError:
        print("正在安装PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✓ PyInstaller安装成功")
            return True
        except:
            print("✗ PyInstaller安装失败")
            return False

def build_exe_fixed():
    """构建修复后的可执行文件"""
    print("开始构建修复后的可执行文件...")
    
    # 使用控制台模式而不是窗口模式，避免stdin/stdout问题
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",  # 单文件模式
        "--console",  # 使用控制台模式（重要！）
        "--name=污染源异常检测系统V6.0_修复版",
        "--add-data=V6_0核心算法模块.py;.",
        "--add-data=污染源异常检测GUI系统.py;.",
        "--add-data=../城市在线监测流量异常检测系统/城市污染源异常检测系统.py;.",
        "--hidden-import=tkinter",
        "--hidden-import=tkinter.ttk",
        "--hidden-import=tkinter.filedialog",
        "--hidden-import=tkinter.messagebox",
        "--hidden-import=tkinter.scrolledtext",
        "--hidden-import=pandas",
        "--hidden-import=numpy",
        "--hidden-import=matplotlib",
        "--hidden-import=matplotlib.pyplot",
        "--hidden-import=matplotlib.backends.backend_tkagg",
        "--hidden-import=sklearn",
        "--hidden-import=openpyxl",
        "--hidden-import=xlrd",
        "--collect-all=matplotlib",
        "--collect-all=tkinter",
        "启动GUI系统.py"
    ]
    
    try:
        print("执行PyInstaller命令...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 构建成功")
            return True
        else:
            print("✗ 构建失败")
            print("错误信息:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 构建异常: {str(e)}")
        return False

def copy_exe_fixed():
    """复制修复后的可执行文件"""
    source = "dist/污染源异常检测系统V6.0_修复版.exe"
    target = "污染源异常检测系统V6.0_修复版.exe"
    
    if os.path.exists(source):
        try:
            shutil.copy2(source, target)
            size_mb = os.path.getsize(target) / (1024 * 1024)
            print(f"✓ 修复版可执行文件已生成: {target} ({size_mb:.1f} MB)")
            return True
        except Exception as e:
            print(f"✗ 复制文件失败: {str(e)}")
            return False
    else:
        print(f"✗ 找不到生成的文件: {source}")
        return False

def cleanup_fixed():
    """清理临时文件"""
    print("清理临时文件...")
    dirs_to_remove = ['build', 'dist', '__pycache__']
    files_to_remove = ['污染源异常检测系统V6.0_修复版.spec']
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✓ 删除: {dir_name}")
            except:
                print(f"⚠️ 无法删除: {dir_name}")
    
    for file_name in files_to_remove:
        if os.path.exists(file_name):
            try:
                os.remove(file_name)
                print(f"✓ 删除: {file_name}")
            except:
                print(f"⚠️ 无法删除: {file_name}")

def create_startup_batch():
    """创建启动批处理文件"""
    batch_content = '''@echo off
echo 启动污染源异常检测系统V6.0...
echo.
echo 如果程序无法启动，请检查：
echo 1. 系统是否为Windows 7/8/10/11 64位
echo 2. 是否有足够的磁盘空间
echo 3. 数据文件夹是否存在
echo.
pause
"污染源异常检测系统V6.0_修复版.exe"
pause
'''
    
    try:
        with open("启动系统.bat", "w", encoding="gbk") as f:
            f.write(batch_content)
        print("✓ 启动批处理文件创建成功: 启动系统.bat")
    except Exception as e:
        print(f"⚠️ 创建批处理文件失败: {str(e)}")

def create_fixed_readme():
    """创建修复版使用说明"""
    readme_content = """# 污染源异常检测系统V6.0 - 修复版使用说明

## 修复内容
- 解决了PyInstaller打包后的stdin/stdout问题
- 改用控制台模式启动，提高稳定性
- 增强了错误处理机制

## 启动方法
方法1：双击 "污染源异常检测系统V6.0_修复版.exe"
方法2：双击 "启动系统.bat" （推荐，有错误提示）

## 注意事项
1. 修复版使用控制台模式，启动时会显示一个黑色控制台窗口，这是正常现象
2. 控制台窗口会显示程序运行状态，请不要关闭它
3. GUI界面会在控制台窗口启动后出现
4. 如果程序无法启动，控制台窗口会显示错误信息

## 系统要求
- Windows 7/8/10/11 (64位)
- 至少 4GB 内存
- 至少 500MB 可用磁盘空间

## 故障排除
如果程序仍然无法启动：
1. 检查系统是否满足最低要求
2. 确认有足够的磁盘空间
3. 检查数据文件夹是否存在
4. 尝试以管理员身份运行

修复版本生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    try:
        from datetime import datetime
        with open("修复版使用说明.txt", "w", encoding="utf-8") as f:
            f.write(readme_content.format(datetime=datetime))
        print("✓ 修复版使用说明创建成功")
    except Exception as e:
        print(f"⚠️ 创建使用说明失败: {str(e)}")

def main():
    """主函数"""
    print("污染源异常检测系统V6.0 - 修复版构建工具")
    print("=" * 60)
    
    # 检查必要文件
    required_files = [
        "启动GUI系统.py",
        "污染源异常检测GUI系统.py", 
        "V6_0核心算法模块.py"
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"✗ 缺少必要文件: {missing_files}")
        return False
    
    print("✓ 必要文件检查通过")
    
    # 构建步骤
    steps = [
        ("安装PyInstaller", install_pyinstaller),
        ("构建修复版exe", build_exe_fixed),
        ("复制可执行文件", copy_exe_fixed),
        ("创建启动批处理", create_startup_batch),
        ("创建使用说明", create_fixed_readme),
        ("清理临时文件", cleanup_fixed)
    ]
    
    for step_name, step_func in steps:
        print(f"\n--- {step_name} ---")
        if not step_func():
            print(f"❌ {step_name}失败")
            return False
    
    print("\n" + "=" * 60)
    print("🎉 修复版构建完成！")
    print("\n生成的文件:")
    print("- 污染源异常检测系统V6.0_修复版.exe (主程序)")
    print("- 启动系统.bat (推荐启动方式)")
    print("- 修复版使用说明.txt (使用说明)")
    print("\n建议使用 '启动系统.bat' 启动程序，可以看到详细的启动信息。")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n构建失败")
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"\n异常: {str(e)}")
        input("\n按回车键退出...")
