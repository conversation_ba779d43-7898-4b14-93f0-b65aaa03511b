#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的可执行文件构建脚本
"""

import os
import sys
import subprocess
import shutil

def install_pyinstaller():
    """安装PyInstaller"""
    print("检查PyInstaller...")
    try:
        import PyInstaller
        print("✓ PyInstaller已安装")
        return True
    except ImportError:
        print("正在安装PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✓ PyInstaller安装成功")
            return True
        except:
            print("✗ PyInstaller安装失败")
            return False

def build_exe():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    
    # PyInstaller命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",  # 单文件模式
        "--windowed",  # 无控制台窗口
        "--name=污染源异常检测系统V6.0",
        "--add-data=V6_0核心算法模块.py;.",
        "--add-data=污染源异常检测GUI系统.py;.",
        "--add-data=../城市在线监测流量异常检测系统/城市污染源异常检测系统.py;.",
        "--hidden-import=tkinter",
        "--hidden-import=tkinter.ttk",
        "--hidden-import=tkinter.filedialog",
        "--hidden-import=tkinter.messagebox",
        "--hidden-import=tkinter.scrolledtext",
        "--hidden-import=pandas",
        "--hidden-import=numpy",
        "--hidden-import=matplotlib",
        "--hidden-import=matplotlib.pyplot",
        "--hidden-import=matplotlib.backends.backend_tkagg",
        "--hidden-import=sklearn",
        "--hidden-import=openpyxl",
        "启动GUI系统.py"
    ]
    
    try:
        print("执行PyInstaller命令...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 构建成功")
            return True
        else:
            print("✗ 构建失败")
            print("错误信息:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 构建异常: {str(e)}")
        return False

def copy_exe():
    """复制可执行文件"""
    source = "dist/污染源异常检测系统V6.0.exe"
    target = "污染源异常检测系统V6.0.exe"
    
    if os.path.exists(source):
        try:
            shutil.copy2(source, target)
            size_mb = os.path.getsize(target) / (1024 * 1024)
            print(f"✓ 可执行文件已生成: {target} ({size_mb:.1f} MB)")
            return True
        except Exception as e:
            print(f"✗ 复制文件失败: {str(e)}")
            return False
    else:
        print(f"✗ 找不到生成的文件: {source}")
        return False

def cleanup():
    """清理临时文件"""
    print("清理临时文件...")
    dirs_to_remove = ['build', 'dist', '__pycache__']
    files_to_remove = ['污染源异常检测系统V6.0.spec']
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✓ 删除: {dir_name}")
            except:
                print(f"⚠️ 无法删除: {dir_name}")
    
    for file_name in files_to_remove:
        if os.path.exists(file_name):
            try:
                os.remove(file_name)
                print(f"✓ 删除: {file_name}")
            except:
                print(f"⚠️ 无法删除: {file_name}")

def create_usage_guide():
    """创建使用指南"""
    guide_content = """# 污染源异常检测系统 V6.0 使用指南

## 快速开始
1. 双击 `污染源异常检测系统V6.0.exe` 启动程序
2. 在"数据输入"选项卡选择Excel数据文件
3. 在"参数配置"选项卡调整检测参数（可选）
4. 在"程序执行"选项卡点击"开始分析"
5. 等待分析完成，查看生成的报告和图表

## 数据文件要求
Excel文件必须包含以下列：
- 企业名称
- 监测点名称
- data_time（时间）
- 流量

## 输出结果
- Excel分析报告
- PNG格式散点图
- 时间段分析汇总

## 注意事项
- 确保有足够磁盘空间（建议至少1GB）
- 数据量建议不少于100个数据点
- 分析时间取决于数据量大小

如有问题，请检查数据文件格式是否正确。
"""
    
    try:
        with open("使用指南.txt", "w", encoding="utf-8") as f:
            f.write(guide_content)
        print("✓ 使用指南创建成功")
    except Exception as e:
        print(f"⚠️ 创建使用指南失败: {str(e)}")

def main():
    """主函数"""
    print("污染源异常检测系统 V6.0 - 简化构建工具")
    print("=" * 50)
    
    # 检查必要文件
    required_files = [
        "启动GUI系统.py",
        "污染源异常检测GUI系统.py", 
        "V6_0核心算法模块.py"
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"✗ 缺少必要文件: {missing_files}")
        return False
    
    print("✓ 必要文件检查通过")
    
    # 构建步骤
    steps = [
        ("安装PyInstaller", install_pyinstaller),
        ("构建可执行文件", build_exe),
        ("复制可执行文件", copy_exe),
        ("创建使用指南", create_usage_guide),
        ("清理临时文件", cleanup)
    ]
    
    for step_name, step_func in steps:
        print(f"\n--- {step_name} ---")
        if not step_func():
            print(f"❌ {step_name}失败")
            return False
    
    print("\n" + "=" * 50)
    print("🎉 构建完成！")
    print("\n生成的文件:")
    print("- 污染源异常检测系统V6.0.exe")
    print("- 使用指南.txt")
    print("\n现在可以运行 .exe 文件了！")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n构建失败")
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"\n异常: {str(e)}")
        input("\n按回车键退出...")
