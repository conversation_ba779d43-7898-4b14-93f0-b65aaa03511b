#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化测试脚本 - 测试核心功能而不启动GUI
"""

import sys
import os
import traceback

def test_core_functionality():
    """测试核心功能"""
    print("=== 测试核心功能 ===")
    
    try:
        # 测试V6.0核心算法模块
        from V6_0核心算法模块 import (
            detect_breakpoints_with_config,
            classify_segment_status_with_config,
            calculate_protection_interval_with_config,
            detect_segment_anomalies_with_config
        )
        print("✓ V6.0核心算法模块导入成功")
        
        # 测试城市污染源异常检测系统
        sys.path.append('..')
        sys.path.append('../城市在线监测流量异常检测系统')
        from 城市污染源异常检测系统 import CityAnomalyDetectionSystem
        print("✓ 城市污染源异常检测系统导入成功")
        
        # 创建测试数据
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta
        
        timestamps = [datetime.now() + timedelta(hours=i) for i in range(100)]
        flow_data = np.random.normal(10, 2, 100)
        flow_data[20:30] = 0  # 零值
        flow_data[50] = -1  # 负值
        
        # 测试配置
        config = {
            'window_size': 24,
            'cv_threshold': 0.8,
            'fixed_threshold': 0.5,
            'min_distance': 12,
            'shutdown_threshold': 0.7,
            'low_value_limit': 0.2,
            'single_state_cv_max': 0.3,
            'protection_std_small': 0.1,
            'protection_std_medium': 0.5,
            'protection_factor_small': 1.5,
            'protection_factor_medium': 1.0,
            'protection_factor_large': 0.5,
            'use_p5_p95': True,
            'use_iqr': True,
            'use_mad': True,
            'p5_p95_weight': 3,
            'iqr_weight': 3,
            'mad_weight': 3,
            'negative_weight': 5,
            'mad_multiplier': 2.5,
            'iqr_multiplier': 1.5
        }
        
        # 测试各个功能
        breakpoints = detect_breakpoints_with_config(flow_data, timestamps, config)
        print(f"✓ 间断点检测: {len(breakpoints)} 个间断点")
        
        status = classify_segment_status_with_config(pd.Series(flow_data), config)
        print(f"✓ 状态分类: {status['status']}")
        
        protection = calculate_protection_interval_with_config(pd.Series(flow_data), config)
        print(f"✓ 保护区间: [{protection['lower']:.2f}, {protection['upper']:.2f}]")
        
        test_data = pd.DataFrame({'timestamp': timestamps, 'flow_value': flow_data})
        anomalies = detect_segment_anomalies_with_config(test_data, status, config)
        print(f"✓ 异常检测: {anomalies['total_anomalies']} 个异常")
        
        return True
        
    except Exception as e:
        print(f"✗ 核心功能测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_data_access():
    """测试数据访问"""
    print("\n=== 测试数据访问 ===")
    
    try:
        data_folder = "../数据读取"
        if os.path.exists(data_folder):
            files = [f for f in os.listdir(data_folder) if f.endswith('.xlsx')]
            print(f"✓ 找到 {len(files)} 个数据文件")
            
            if files:
                # 测试读取第一个文件
                import pandas as pd
                test_file = os.path.join(data_folder, files[0])
                df = pd.read_excel(test_file)
                print(f"✓ 成功读取测试文件: {files[0]} ({len(df)} 行)")
                
                # 检查必要列
                required_cols = ['企业名称', '监测点名称', 'data_time', '流量']
                missing_cols = [col for col in required_cols if col not in df.columns]
                
                if not missing_cols:
                    print("✓ 数据文件格式正确")
                else:
                    print(f"⚠️ 缺少列: {missing_cols}")
                
                return True
            else:
                print("⚠️ 没有找到Excel数据文件")
                return False
        else:
            print(f"✗ 数据文件夹不存在: {data_folder}")
            return False
            
    except Exception as e:
        print(f"✗ 数据访问测试失败: {str(e)}")
        return False

def test_output_directory():
    """测试输出目录"""
    print("\n=== 测试输出目录 ===")
    
    try:
        output_base = "../检测报告"
        
        # 创建测试输出目录
        from datetime import datetime
        timestamp = datetime.now().strftime("%m-%d-%H-%M-%S")
        test_output_dir = os.path.join(output_base, f"test_{timestamp}")
        
        os.makedirs(test_output_dir, exist_ok=True)
        print(f"✓ 创建输出目录: {test_output_dir}")
        
        # 测试写入权限
        test_file = os.path.join(test_output_dir, "test.txt")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("测试文件")
        print("✓ 输出目录写入权限正常")
        
        # 清理测试文件
        os.remove(test_file)
        os.rmdir(test_output_dir)
        print("✓ 测试清理完成")
        
        return True
        
    except Exception as e:
        print(f"✗ 输出目录测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("污染源异常检测GUI系统 - 简化测试")
    print("=" * 50)
    
    tests = [
        ("核心功能", test_core_functionality),
        ("数据访问", test_data_access),
        ("输出目录", test_output_directory)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 核心功能测试全部通过！")
        print("\n下一步:")
        print("1. 运行 'python 污染源异常检测GUI系统.py' 启动GUI")
        print("2. 在GUI中选择数据文件进行分析")
        print("3. 调整参数配置")
        print("4. 执行分析并查看结果")
    else:
        print("⚠️ 部分测试失败，请检查相关问题")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
