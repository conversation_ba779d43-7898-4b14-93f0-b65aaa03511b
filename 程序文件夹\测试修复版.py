#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复版exe文件
"""

import os
import sys
import subprocess
import time

def test_fixed_exe():
    """测试修复版exe文件"""
    exe_path = "污染源异常检测系统V6.0_修复版.exe"
    
    print("=== 测试修复版exe文件 ===")
    
    if not os.path.exists(exe_path):
        print(f"✗ 修复版exe文件不存在: {exe_path}")
        return False
    
    size_mb = os.path.getsize(exe_path) / (1024 * 1024)
    print(f"✓ 修复版exe文件存在: {exe_path}")
    print(f"✓ 文件大小: {size_mb:.1f} MB")
    
    try:
        print("尝试启动修复版exe文件...")
        
        # 启动进程（控制台模式）
        process = subprocess.Popen(
            [exe_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"✓ 进程已启动，PID: {process.pid}")
        
        # 等待5秒检查进程状态
        time.sleep(5)
        
        # 检查进程是否还在运行
        poll_result = process.poll()
        
        if poll_result is None:
            print("✓ 进程仍在运行，程序启动成功")
            
            # 尝试终止进程
            try:
                process.terminate()
                process.wait(timeout=10)
                print("✓ 进程已正常终止")
            except subprocess.TimeoutExpired:
                process.kill()
                print("⚠️ 进程被强制终止")
            
            return True
        else:
            # 进程已退出，检查错误
            stdout, stderr = process.communicate()
            print(f"✗ 进程已退出，返回码: {poll_result}")
            
            if stderr:
                print("错误输出:")
                print(stderr)
            
            if stdout:
                print("标准输出:")
                print(stdout)
            
            return False
            
    except Exception as e:
        print(f"✗ 启动修复版exe失败: {str(e)}")
        return False

def test_batch_file():
    """测试批处理文件"""
    batch_path = "启动系统.bat"
    
    print("\n=== 测试批处理文件 ===")
    
    if not os.path.exists(batch_path):
        print(f"✗ 批处理文件不存在: {batch_path}")
        return False
    
    print(f"✓ 批处理文件存在: {batch_path}")
    
    # 读取批处理文件内容
    try:
        with open(batch_path, 'r', encoding='gbk') as f:
            content = f.read()
        print("✓ 批处理文件内容:")
        print(content[:200] + "..." if len(content) > 200 else content)
        return True
    except Exception as e:
        print(f"✗ 读取批处理文件失败: {str(e)}")
        return False

def compare_versions():
    """比较两个版本的文件大小"""
    print("\n=== 版本比较 ===")
    
    original_exe = "污染源异常检测系统V6.0.exe"
    fixed_exe = "污染源异常检测系统V6.0_修复版.exe"
    
    if os.path.exists(original_exe):
        original_size = os.path.getsize(original_exe) / (1024 * 1024)
        print(f"原版文件大小: {original_size:.1f} MB")
    else:
        print("原版文件不存在")
    
    if os.path.exists(fixed_exe):
        fixed_size = os.path.getsize(fixed_exe) / (1024 * 1024)
        print(f"修复版文件大小: {fixed_size:.1f} MB")
    else:
        print("修复版文件不存在")
    
    if os.path.exists(original_exe) and os.path.exists(fixed_exe):
        diff = fixed_size - original_size
        print(f"大小差异: {diff:+.1f} MB")

def main():
    """主函数"""
    print("污染源异常检测系统V6.0 - 修复版测试")
    print("=" * 50)
    
    tests = [
        ("修复版exe测试", test_fixed_exe),
        ("批处理文件测试", test_batch_file),
        ("版本比较", compare_versions)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_name == "版本比较":
                test_func()  # 这个测试不返回布尔值
                results.append((test_name, True))
            else:
                result = test_func()
                results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}异常: {str(e)}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n测试: {passed}/{total} 项通过")
    
    if passed >= 2:
        print("\n🎉 修复版测试基本通过！")
        print("\n使用建议:")
        print("1. 优先使用 '启动系统.bat' 启动程序")
        print("2. 如果看到控制台窗口，这是正常现象，不要关闭")
        print("3. GUI界面会在控制台启动后出现")
        print("4. 如有问题，控制台会显示详细错误信息")
    else:
        print("\n⚠️ 修复版测试失败")
        print("请检查构建过程是否有问题")
    
    return passed >= 2

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n测试异常: {str(e)}")
        input("\n按回车键退出...")
