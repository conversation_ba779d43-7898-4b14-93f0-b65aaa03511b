#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的城市污染源异常检测系统
移除sklearn依赖，专门用于V6.0 GUI系统
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.dates import DateFormatter
import warnings
import os
from datetime import datetime

# 设置中文字体和忽略警告
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore')

class CityAnomalyDetectionSystem:
    """简化的城市污染源异常检测系统（无sklearn依赖）"""
    
    def __init__(self, city_name="城市", data_folder=None):
        """
        初始化系统
        
        Args:
            city_name: 城市名称，用于报告标题和文件命名
            data_folder: 数据文件夹路径，默认为"{city_name}导出污染源"
        """
        self.city_name = city_name
        self.data_folder = data_folder or f"{city_name}导出污染源"
        self.processed_data = None
        self.monthly_data = {}
        self.site_profiles = {}
        self.monthly_analysis = {}
        self.comprehensive_results = {}
        
        # 创建统一的报告目录
        timestamp = datetime.now().strftime("%m-%d-%H-%M-%S")
        self.report_dir = os.path.join("..", "检测报告", timestamp)
        os.makedirs(self.report_dir, exist_ok=True)
        
        # 系统配置
        self.config = {
            'min_samples_per_month': 30,
        }
        
        print(f"初始化 {self.city_name} 污染源异常检测系统（简化版）")
        print(f"报告输出目录: {self.report_dir}")
    
    def _preprocess_data(self, df):
        """预处理数据"""
        try:
            # 确保必要的列存在
            required_columns = ['企业名称', '监测点名称', 'data_time', '流量']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                raise ValueError(f"数据缺少必要的列: {missing_columns}")
            
            # 重命名列以保持一致性
            df = df.rename(columns={
                '企业名称': 'company_name',
                '监测点名称': 'site_name',
                'data_time': 'timestamp',
                '流量': 'flow_value'
            })
            
            # 转换时间戳
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # 转换流量值为数值类型
            df['flow_value'] = pd.to_numeric(df['flow_value'], errors='coerce')
            
            # 添加月份信息
            if '数据月份' in df.columns:
                df['month'] = df['数据月份']
            else:
                df['month'] = df['timestamp'].dt.month
            
            # 移除无效数据
            df = df.dropna(subset=['flow_value', 'timestamp'])
            
            print(f"数据预处理完成，共 {len(df)} 条有效记录")
            
            return df
            
        except Exception as e:
            print(f"数据预处理失败: {str(e)}")
            raise
    
    def _organize_monthly_data(self):
        """按月份组织数据"""
        if self.processed_data is None:
            raise ValueError("请先加载和预处理数据")
        
        self.monthly_data = {}
        
        for month in self.processed_data['month'].unique():
            month_data = self.processed_data[self.processed_data['month'] == month].copy()
            self.monthly_data[month] = month_data
            
            print(f"月份 {month}: {len(month_data)} 条记录")
        
        print(f"数据已按月份组织，共 {len(self.monthly_data)} 个月份")
    
    def load_and_preprocess_data(self):
        """加载和预处理数据"""
        try:
            # 检查数据文件夹
            if not os.path.exists(self.data_folder):
                print(f"数据文件夹不存在: {self.data_folder}")
                return False
            
            # 查找Excel文件
            excel_files = []
            for file in os.listdir(self.data_folder):
                if file.endswith(('.xlsx', '.xls')):
                    excel_files.append(os.path.join(self.data_folder, file))
            
            if not excel_files:
                print(f"在 {self.data_folder} 中未找到Excel文件")
                return False
            
            # 读取所有Excel文件
            all_data = []
            for file_path in excel_files:
                try:
                    df = pd.read_excel(file_path)
                    all_data.append(df)
                    print(f"成功读取: {os.path.basename(file_path)} ({len(df)} 条记录)")
                except Exception as e:
                    print(f"读取文件失败 {file_path}: {str(e)}")
            
            if not all_data:
                print("没有成功读取任何数据文件")
                return False
            
            # 合并所有数据
            combined_data = pd.concat(all_data, ignore_index=True)
            print(f"合并数据完成，总计 {len(combined_data)} 条记录")
            
            # 预处理数据
            self.processed_data = self._preprocess_data(combined_data)
            
            # 按月份组织数据
            self._organize_monthly_data()
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {str(e)}")
            return False
    
    def get_site_data(self, company_name, site_name, month=None):
        """获取指定站点的数据"""
        if month is not None:
            if month not in self.monthly_data:
                return pd.DataFrame()
            data = self.monthly_data[month]
        else:
            data = self.processed_data
        
        site_data = data[
            (data['company_name'] == company_name) & 
            (data['site_name'] == site_name)
        ].copy()
        
        return site_data.sort_values('timestamp')
    
    def get_available_sites(self, month=None):
        """获取可用的站点列表"""
        if month is not None:
            if month not in self.monthly_data:
                return []
            data = self.monthly_data[month]
        else:
            data = self.processed_data
        
        sites = data.groupby(['company_name', 'site_name']).size().reset_index()
        return [(row['company_name'], row['site_name']) for _, row in sites.iterrows()]
    
    def generate_basic_statistics(self, site_data):
        """生成基础统计信息"""
        if len(site_data) == 0:
            return {}
        
        flow_values = site_data['flow_value']
        
        stats = {
            'count': len(flow_values),
            'mean': flow_values.mean(),
            'median': flow_values.median(),
            'std': flow_values.std(),
            'min': flow_values.min(),
            'max': flow_values.max(),
            'zero_count': (flow_values == 0).sum(),
            'negative_count': (flow_values < 0).sum(),
            'positive_count': (flow_values > 0).sum(),
            'zero_ratio': (flow_values == 0).sum() / len(flow_values),
            'negative_ratio': (flow_values < 0).sum() / len(flow_values),
        }
        
        # 计算变异系数
        if stats['mean'] > 0:
            stats['cv'] = stats['std'] / stats['mean']
        else:
            stats['cv'] = 0
        
        return stats
    
    def simple_anomaly_detection(self, flow_values, method='iqr'):
        """简单的异常检测方法"""
        anomaly_indices = []
        
        if len(flow_values) < 3:
            return anomaly_indices
        
        # 过滤正值
        positive_values = flow_values[flow_values > 0]
        if len(positive_values) < 3:
            return anomaly_indices
        
        if method == 'iqr':
            # IQR方法
            q1 = positive_values.quantile(0.25)
            q3 = positive_values.quantile(0.75)
            iqr = q3 - q1
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            for i, val in enumerate(flow_values):
                if val > 0 and (val < lower_bound or val > upper_bound):
                    anomaly_indices.append(i)
        
        elif method == 'percentile':
            # 百分位数方法
            p5 = positive_values.quantile(0.05)
            p95 = positive_values.quantile(0.95)
            
            for i, val in enumerate(flow_values):
                if val > 0 and (val < p5 or val > p95):
                    anomaly_indices.append(i)
        
        elif method == 'zscore':
            # Z-score方法
            mean_val = positive_values.mean()
            std_val = positive_values.std()
            
            if std_val > 0:
                for i, val in enumerate(flow_values):
                    if val > 0:
                        z_score = abs(val - mean_val) / std_val
                        if z_score > 2.5:  # 2.5个标准差
                            anomaly_indices.append(i)
        
        # 添加负值异常
        for i, val in enumerate(flow_values):
            if val < 0:
                anomaly_indices.append(i)
        
        return sorted(list(set(anomaly_indices)))
    
    def classify_operation_status(self, flow_values, threshold_ratio=0.7, low_value_limit=0.2, cv_threshold=0.3):
        """分类运行状态"""
        if len(flow_values) == 0:
            return {'status': '无数据', 'confidence': 0.0}
        
        # 计算零值和极低值比例
        low_value_count = (flow_values <= low_value_limit).sum()
        low_value_ratio = low_value_count / len(flow_values)
        
        # 停运状态
        if low_value_ratio >= threshold_ratio:
            return {
                'status': '停运状态',
                'confidence': min(0.95, 0.5 + low_value_ratio),
                'low_value_ratio': low_value_ratio,
                'description': f'零值或极低值比例{low_value_ratio:.1%}，判定为停运状态'
            }
        
        # 计算变异系数
        positive_data = flow_values[flow_values > low_value_limit]
        if len(positive_data) < 3:
            return {
                'status': '停运状态',
                'confidence': 0.8,
                'low_value_ratio': low_value_ratio,
                'description': '有效数据点过少，判定为停运状态'
            }
        
        mean_val = positive_data.mean()
        std_val = positive_data.std()
        cv = std_val / mean_val if mean_val > 0 else 0
        
        # 单状态运行
        if cv < cv_threshold:
            return {
                'status': '单状态运行',
                'confidence': min(0.95, 0.5 + (1 - cv)),
                'cv': cv,
                'low_value_ratio': low_value_ratio,
                'description': f'变异系数{cv:.3f}，数值相对稳定，判定为单状态运行'
            }
        
        # 正常波动
        else:
            return {
                'status': '正常波动',
                'confidence': min(0.95, 0.5 + min(cv, 1.0)),
                'cv': cv,
                'low_value_ratio': low_value_ratio,
                'description': f'变异系数{cv:.3f}，存在明显变化，判定为正常波动'
            }


def main():
    """主程序"""
    print("=" * 60)
    print("简化城市污染源异常检测系统（无sklearn依赖）")
    print("=" * 60)
    
    # 测试系统
    system = CityAnomalyDetectionSystem(city_name="测试城市")
    
    print("系统初始化完成")
    print("注意：这是简化版本，移除了sklearn依赖")
    print("适用于V6.0 GUI系统的基础数据处理")


if __name__ == "__main__":
    main()
