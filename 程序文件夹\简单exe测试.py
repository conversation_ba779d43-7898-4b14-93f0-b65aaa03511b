#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的exe文件测试脚本
"""

import os
import sys
import subprocess
import time

def check_exe_file():
    """检查exe文件"""
    exe_path = "污染源异常检测系统V6.0.exe"
    
    print("=== 检查exe文件 ===")
    
    if os.path.exists(exe_path):
        size_mb = os.path.getsize(exe_path) / (1024 * 1024)
        print(f"✓ exe文件存在: {exe_path}")
        print(f"✓ 文件大小: {size_mb:.1f} MB")
        
        # 检查文件权限
        if os.access(exe_path, os.R_OK):
            print("✓ 文件可读")
        else:
            print("✗ 文件不可读")
            
        if os.access(exe_path, os.X_OK):
            print("✓ 文件可执行")
        else:
            print("✗ 文件不可执行")
            
        return True
    else:
        print(f"✗ exe文件不存在: {exe_path}")
        return False

def test_exe_startup():
    """测试exe启动"""
    exe_path = "污染源异常检测系统V6.0.exe"
    
    print("\n=== 测试exe启动 ===")
    
    try:
        print("尝试启动exe文件...")
        
        # 启动进程
        process = subprocess.Popen(
            [exe_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NEW_CONSOLE
        )
        
        print(f"✓ 进程已启动，PID: {process.pid}")
        
        # 等待3秒检查进程状态
        time.sleep(3)
        
        # 检查进程是否还在运行
        poll_result = process.poll()
        
        if poll_result is None:
            print("✓ 进程仍在运行，程序启动成功")
            
            # 尝试终止进程
            try:
                process.terminate()
                process.wait(timeout=5)
                print("✓ 进程已正常终止")
            except subprocess.TimeoutExpired:
                process.kill()
                print("⚠️ 进程被强制终止")
            
            return True
        else:
            # 进程已退出，检查错误
            stdout, stderr = process.communicate()
            print(f"✗ 进程已退出，返回码: {poll_result}")
            
            if stderr:
                print("错误输出:")
                print(stderr.decode('utf-8', errors='ignore'))
            
            if stdout:
                print("标准输出:")
                print(stdout.decode('utf-8', errors='ignore'))
            
            return False
            
    except FileNotFoundError:
        print("✗ 找不到exe文件")
        return False
    except Exception as e:
        print(f"✗ 启动exe失败: {str(e)}")
        return False

def check_dependencies():
    """检查依赖文件"""
    print("\n=== 检查依赖文件 ===")
    
    # 检查源代码文件
    source_files = [
        "污染源异常检测GUI系统.py",
        "V6_0核心算法模块.py",
        "启动GUI系统.py"
    ]
    
    for file in source_files:
        if os.path.exists(file):
            print(f"✓ 源文件存在: {file}")
        else:
            print(f"✗ 源文件缺失: {file}")
    
    # 检查上级目录的系统文件
    parent_system_file = "../城市在线监测流量异常检测系统/城市污染源异常检测系统.py"
    if os.path.exists(parent_system_file):
        print(f"✓ 系统文件存在: {parent_system_file}")
    else:
        print(f"✗ 系统文件缺失: {parent_system_file}")
    
    # 检查数据文件夹
    data_folder = "../数据读取"
    if os.path.exists(data_folder):
        excel_files = [f for f in os.listdir(data_folder) if f.endswith(('.xlsx', '.xls'))]
        print(f"✓ 数据文件夹存在，包含 {len(excel_files)} 个Excel文件")
    else:
        print(f"✗ 数据文件夹不存在: {data_folder}")

def create_test_data():
    """创建测试数据"""
    print("\n=== 创建测试数据 ===")
    
    try:
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta
        
        # 创建测试数据
        start_time = datetime.now() - timedelta(days=1)
        timestamps = [start_time + timedelta(hours=i) for i in range(100)]
        
        # 生成模拟流量数据
        np.random.seed(42)  # 固定随机种子
        flow_data = np.random.normal(10, 2, 100)
        flow_data[20:30] = 0  # 添加零值段
        flow_data[50] = -1   # 添加负值
        flow_data[80:85] = np.random.normal(25, 1, 5)  # 添加异常高值
        
        test_data = {
            '企业名称': ['测试企业'] * 100,
            '监测点名称': ['测试监测点'] * 100,
            'data_time': timestamps,
            '流量': flow_data,
            '数据月份': [start_time.month] * 100
        }
        
        df = pd.DataFrame(test_data)
        test_file = "测试数据.xlsx"
        df.to_excel(test_file, index=False)
        
        print(f"✓ 测试数据文件创建成功: {test_file}")
        print(f"  - 数据点数: {len(df)}")
        print(f"  - 时间范围: {timestamps[0].strftime('%Y-%m-%d %H:%M')} 至 {timestamps[-1].strftime('%Y-%m-%d %H:%M')}")
        print(f"  - 流量范围: {flow_data.min():.2f} 至 {flow_data.max():.2f}")
        
        return test_file
        
    except ImportError as e:
        print(f"✗ 缺少必要模块: {str(e)}")
        return None
    except Exception as e:
        print(f"✗ 创建测试数据失败: {str(e)}")
        return None

def manual_test_instructions():
    """手动测试说明"""
    print("\n=== 手动测试说明 ===")
    print("请按照以下步骤手动测试exe程序:")
    print("1. 双击 '污染源异常检测系统V6.0.exe' 启动程序")
    print("2. 观察程序是否正常启动并显示GUI界面")
    print("3. 检查界面是否包含4个选项卡：数据输入、参数配置、程序执行、系统说明")
    print("4. 在'数据输入'选项卡中尝试选择Excel文件")
    print("5. 在'参数配置'选项卡中查看参数设置")
    print("6. 在'程序执行'选项卡中尝试运行分析")
    print("7. 在'系统说明'选项卡中查看文档内容")
    print("\n如果程序无法启动或出现错误，请记录具体的错误信息。")

def main():
    """主函数"""
    print("污染源异常检测系统V6.0 - exe文件测试")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("检查exe文件", check_exe_file),
        ("检查依赖文件", check_dependencies),
        ("创建测试数据", create_test_data),
        ("测试exe启动", test_exe_startup),
    ]
    
    results = []
    test_data_file = None
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_name == "创建测试数据":
                result = test_func()
                if result:
                    test_data_file = result
                    results.append((test_name, True))
                else:
                    results.append((test_name, False))
            else:
                result = test_func()
                results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}异常: {str(e)}")
            results.append((test_name, False))
    
    # 显示手动测试说明
    manual_test_instructions()
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("自动测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n自动测试: {passed}/{total} 项通过")
    
    if test_data_file:
        print(f"\n✓ 测试数据文件已创建: {test_data_file}")
        print("可以在GUI程序中使用此文件进行测试")
    
    if passed >= 3:  # 至少通过3项测试
        print("\n🎉 基础测试通过！")
        print("建议进行手动GUI测试以验证完整功能")
    else:
        print("\n⚠️ 部分基础测试失败")
        print("请检查exe文件和依赖是否正确")
    
    return passed >= 3

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
